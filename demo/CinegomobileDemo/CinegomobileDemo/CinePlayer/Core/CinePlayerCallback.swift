//
//  CinePlayerCallback.swift
//  VideoPlayer
//
//  Created by Zero on 2024/10/3.
//

import Foundation
#if canImport(UIKit)
    import UIKit
#endif

/// callback，用于在播放器各种状态通知外部调用
/// 例如 CinePlayer().onFinish 播放完成后修改历史记录
@MainActor
public extension CinePlayer {
    /// 播放完成
    func onFinish(_ handler: @escaping (PlayerController, Error?) -> Void) -> Self {
        coordinator.onFinish = handler
        return self
    }

    /// 播放状态发生变化
    func onStateChanged(_ handler: @escaping (PlayerController, CinePlayerStatus) -> Void) -> Self {
        coordinator.onStateChanged = handler
        return self
    }

    // 手势滑动回调 传入自定义手势操作
    #if canImport(UIKit)
        func onSwipe(_ handler: @escaping (UISwipeGestureRecognizer.Direction) -> Void) -> Self {
            coordinator.onSwipe = handler
            return self
        }
    #endif
}
