import Foundation
import SwiftUI

#if canImport(UIKit)
    import UIKit
#else
    import AppKit
#endif

// UIViewRepresentable 和 Coordinator 是 SwiftUI 中用于将 UIKit 视图集成到 SwiftUI 界面中的机制。
//
// UIViewRepresentable 是一个协议，用于将 UIKit 视图包装成 SwiftUI 视图。通过实现这个协议，你可以在 SwiftUI 中使用任何 UIKit 视图。
//
// Coordinator 是一个辅助类，用于管理 UIKit 视图和 SwiftUI 视图之间的交互。它通常用于处理委托方法和数据传递。
//
// 实现步骤
// 创建一个符合 UIViewRepresentable 协议的结构体。
// 实现 makeUIView(context:) 方法来创建 UIKit 视图。
// 实现 updateUIView(_:context:) 方法来更新 UIKit 视图。
// 可选：创建一个 Coordinator 类来处理委托和数据传递。
// 在 makeCoordinator() 方法中返回 Coordinator 实例。

/// 创建一个 View 显示播放器，把 UIView 整合到 SwiftUI
/// 使用 Coordinator 实现播放器控制，状态更新到 UI
public struct CinePlayer {
    public private(set) var coordinator: Coordinator
    public let url: URL?
    public let options: PlayerOptions

    public init(coordinator: Coordinator, url: URL? = nil, options: PlayerOptions) {
        self.coordinator = coordinator
        self.url = url
        self.options = options
    }
}

extension CinePlayer: UIViewRepresentable {
    /// 返回给 UIViewRepresentable CinePlayer.init 时传入的 coordinator
    public func makeCoordinator() -> Coordinator {
        coordinator
    }

    #if canImport(UIKit)

        /// 创建 UIView
        public func makeUIView(context: Context) -> UIViewType {
            context.coordinator.makeView(url: url, options: options)
        }

        /// 更新 UIView
        public func updateUIView(_ view: UIViewType, context: Context) {
            updateView(view, context: context)
        }

        /// iOS tvOS真机先调用onDisappear在调用dismantleUIView，但是模拟器就反过来了。
        public static func dismantleUIView(_: UIViewType, coordinator: Coordinator) {
            coordinator.resetPlayer()
        }
    #else

        /// 创建 NSView
        public func makeNSView(context: Context) -> NSViewType {
            context.coordinator.makeView(url: url, options: options)
        }

        /// 更新 NSView
        public func updateNSView(_ view: NSViewType, context: Context) {
            updateView(view, context: context)
        }

        /// macOS先调用onDisappear在调用dismantleNSView
        public static func dismantleNSView(_ view: NSViewType, coordinator: Coordinator) {
            coordinator.resetPlayer()
            view.window?.aspectRatio = CGSize(width: 16, height: 9)
        }
    #endif

    /// 更新 View
    /// 判断 URL 是否发生改变再更新
    @MainActor
    private func updateView(_: UIView, context: Context) {
        if context.coordinator.controller?.url != url {
            _ = context.coordinator.makeView(url: url, options: options)
        }
    }

    /// 可以理解为 SwiftUI 和 UIView 的连接器
    /// 申明在创建 CinePlayer 的地方传入 Coordinator
    /// 可以修改所传入 Coordinator 下的变量来更新播放器状态
    /// 也可以在 SwiftUI 中需要时，获取播放器里面的数据
    @MainActor
    public final class Coordinator: ObservableObject {
        /// 播放进度时间，用于 cinePlayerDelegate 中接收更新，更新进度条
        public var progress = PlayingProgress()

        public var controller: PlayerController? {
            didSet {
                oldValue?.coordinatorDelegate = nil
            }
        }

        /// 创建播放器视图
        public func makeView(url: URL? = nil, options: PlayerOptions) -> UIView {
            // 如果已经存在，就更新 URL
            if let controller {
                if controller.url == url {
                    return controller.videoView ?? UIView()
                }

                controller.coordinatorDelegate = nil
                controller.setNewURL(url: url, options: options)
                controller.coordinatorDelegate = self
                return controller.videoView ?? UIView()
            } else {
                // 创建新的播放器
                let controller = PlayerController(url: url, options: options, coordinatorDelegate: self)
                self.controller = controller
                return controller.videoView ?? UIView()
            }
        }

        /// 播放状态 UI 上用这个来显示加载中
        public var state: CinePlayerStatus {
            controller?.state ?? .initialized
        }

        /// 在 UI 上修改这个值可以控制播放暂停
        @Published
        public var isMuted: Bool = false {
            didSet {
                controller?.isMuted = isMuted
            }
        }

        /// 控制音量
        @Published
        public var playbackVolume: Float = 1.0 {
            didSet {
                controller?.playbackVolume = playbackVolume
            }
        }

        /// 控制画面填充模式
        @Published
        public var isScaleAspectFill = false {
            didSet {
                controller?.viewContentMode = isScaleAspectFill ? .scaleAspectFill : .scaleAspectFit
            }
        }

        /// 控制播放速度
        @Published
        public var playbackRate: Float = 1.0 {
            didSet {
                controller?.playbackRate = playbackRate
            }
        }

        /// 重置播放器
        public func resetPlayer() {
            onStateChanged = nil
            onFinish = nil
            #if canImport(UIKit)
                onSwipe = nil
            #endif
            controller = nil
        }

        @Published
        public var isMaskShow = true {
            didSet {
                if isMaskShow {
                    #if os(macOS)
                        NSCursor.unhide()
                    #endif
                    delayHideTime = 3
                    timer.fireDate = Date.distantPast
                }
            }
        }

        var delayHideTime: Double = 3

        // TODO: shutdown 的时候也要处理定时器
        lazy var timer: Timer = .scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            guard let self else {
                return
            }
            Task { @MainActor in
                if self.delayHideTime <= 0 {
                    self.isMaskShow = false
                    #if os(macOS)
                        NSCursor.setHiddenUntilMouseMoves(true)
                    #endif
                }
                if self.delayHideTime > 0 {
                    self.delayHideTime -= 0.1
                }
            }
        }

        // 在 swiftUI 中设置的回调，可以设置为空
        // 对应 CinePlayerCallback.swift

        /// 结束的时候
        public var onFinish: ((PlayerController, Error?) -> Void)?

        /// 状态改变
        public var onStateChanged: ((PlayerController, CinePlayerStatus) -> Void)?

        // IOS下
        #if canImport(UIKit)
            /// 手势操作
            public var onSwipe: ((UISwipeGestureRecognizer.Direction) -> Void)?
            /// 处理滑动手势
            @objc public func swipeGestureAction(_ recognizer: UISwipeGestureRecognizer) {
                onSwipe?(recognizer.direction)
            }
        #endif
    }
}

/// 时间进度，进度条用的就是这个
/// @Published 表示只要变动。所有调用的地方都会更新
/// 这是一个频繁变化的 model。View 要少用这个
public class PlayingProgress: ObservableObject {
    /// 改成int才不会频繁更新
    @Published
    public var currentTime = 0
    @Published
    public var totalTime = 1
}
