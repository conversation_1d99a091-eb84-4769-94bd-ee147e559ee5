import AVFoundation
import CFFmpeg
import CoreText

// 解包资源，获取媒体信息，获取轨道，注册解码器

@MainActor
public class PlayerItem {
    /// URL 资源地址
    private let url: URL?
    /// 播放器选项
    public let options: PlayerOptions
    /// 操作队列，用于管理异步操作
    private let operationQueue = OperationQueue()
    /// 条件锁，用于线程同步
    private let condition = NSCondition()
    /// 打开操作
    private var openOperation: BlockOperation?
    /// 读取操作
    private var readOperation: BlockOperation?
    /// 关闭操作
    private var closeOperation: BlockOperation?
    /// 格式上下文指针
    private var formatCtx: UnsafeMutablePointer<AVFormatContext>?
    /// 输出格式上下文指针
    private var outputFormatCtx: UnsafeMutablePointer<AVFormatContext>?
    /// 输出数据包指针
    private var outputPacket: UnsafeMutablePointer<AVPacket>?
    /// 流映射，用于映射输入输出流
    private var streamMapping = [Int: Int]()
    /// 寻址完成处理器
    private var seekingCompletionHandler: ((Bool) -> Void)?
    /// 音频时钟
    var audioClock = PlayerClock()
    /// 视频时钟
    var videoClock = PlayerClock()
    /// 上次视频时钟
    var lastVideoClock = PlayerClock()
    /// 没有音频数据可以渲染
    var isAudioStalled = true
    /// 是否是第一次播放
    private var isFirst = true
    /// 是否在寻找
    private var isSeeking = false
    /// 视频音频缓冲列表
    var videoAudioBufferList = [BufferProtocol]()

    /// 所有资产列表
    private(set) var allAssetList = [FFmpegStreamAsset]()
    /// 视频轨道解码器
    var videoTrackDecode: TrackDecode<VideoVTBFrame>?
    /// 音频轨道解码器
    var audioTrackDecode: TrackDecode<AudioFrame>?
    var currentSubtitleStreamIndex: Int32 = 0
    /// 解码器列表
    private var trackDecodeList = [TrackDecodeProtocol]()

    /// 最大帧持续时间
    private var maxFrameDuration = 10.0
    /// 视频自适应比特率状态
    private var videoAdaptation: VideoAdaptationState?
    /// 视频显示计数
    var videoDisplayCount = UInt8(0)
    /// 是否通过字节寻址
    private var seekByBytes = false
    /// 自然尺寸
    public private(set) var naturalSize = CGSize.zero
    /// 自定义 IO 上下文
    private var ioContext: CustomAVIOContext?
    /// 控制器代理
    weak var controllerDelegate: PlayerControllerDelegate?
    /// 视频时钟同步 判断当前帧需要的操作
    private var videoClockDelayCount = 0 // 视频时钟延迟计数
    /// 章节信息
    public private(set) var chapters: [Chapter] = []
    /// 当前播放时间
    public var currentPlaybackTime: TimeInterval {
        sourceState == .seeking ? seekTime : (mainClock().time - startTime).seconds
    }

    /// 寻找时间
    private var seekTime = TimeInterval(0)
    /// 开始时间
    private var startTime = CMTime.zero
    /// 持续时间
    public private(set) var duration: TimeInterval = 0
    /// 文件大小
    public private(set) var fileSize: Double = 0

    /// 错误信息
    private var error: NSError? {
        didSet {
            if error != nil {
                sourceState = .failed
            }
        }
    }

    /// 播放速率
    public var playbackRate: Float {
        get {
            Float(videoClock.rate)
        }
        set {
            audioClock.rate = Double(newValue)
            videoClock.rate = Double(newValue)
        }
    }

    /// 播放器状态 初始化空闲状态
    private var sourceState = CineSourceState.idle {
        didSet {
            switch sourceState {
            case .opened:
                controllerDelegate?.sourceDidOpened()

            case .reading:
                bufferTimer.fireDate = Date.distantPast

            case .closed:
                bufferTimer.invalidate()

            case .failed:
                controllerDelegate?.sourceDidFailed(error: error)
                // 暂停缓冲
                bufferTimer.fireDate = Date.distantFuture

            case .idle, .opening, .seeking, .paused, .finished:
                break
            }
        }
    }

    /// 定时器，用于缓冲
    lazy var bufferTimer: Timer = .scheduledTimer(withTimeInterval: 0.05, repeats: true) { [weak self] _ in

        self?.codecDidChangeBuffer()
    }

    /// 用于获取当前的信息 音频码率、视频码率
    lazy var dynamicInfo = PlayerDynamicInfo { [weak self] in
        // metadata可能会实时变化。所以把它放在DynamicInfo里面
        metadataToDictionary(self?.formatCtx?.pointee.metadata)
    } bytesRead: { [weak self] in
        self?.formatCtx?.pointee.pb?.pointee.bytes_read ?? 0
    } audioBitrate: { [weak self] in
        Int(8 * (self?.audioTrackDecode?.bitrate ?? 0))
    } videoBitrate: { [weak self] in
        Int(8 * (self?.videoTrackDecode?.bitrate ?? 0))
    }

    // MARK: 静态初始化，用于 FFmpeg 日志回调

    private static var onceInitial: Void = {
        // 初始化字幕字体目录，用于存储嵌入到视频中的字幕字体
        if let urls = try? FileManager.default.contentsOfDirectory(at: PlayerOptions.fontsDir, includingPropertiesForKeys: nil) {
            CTFontManagerRegisterFontURLs(urls as CFArray, .process, true, nil)
        }

        var result = avformat_network_init()
        // 设置日志回调，后续ffmpeg的日志会通过这个回调来输出
        av_log_set_callback { _, level, format, args in
            guard let format else {
                return
            }
            var log = String(cString: format)
            let arguments: CVaListPointer? = args
            if let arguments {
                log = NSString(format: log, arguments: arguments) as String
            }

            // 找不到解码器
            if log.hasPrefix("parser not found for codec") {
                CinePlayerLog(level: .error, log)
            }

            CinePlayerLog(level: .debug, log)
        }
    }()

    // MARK: 初始化

    public init(url: URL?, options: PlayerOptions) {
        self.url = url
        self.options = options
        // 暂停缓冲
        bufferTimer.fireDate = Date.distantFuture
        operationQueue.name = "CinePlayer_" + String(describing: self).components(separatedBy: ".").last!
        operationQueue.maxConcurrentOperationCount = 1
        operationQueue.qualityOfService = .userInteractive
        _ = PlayerItem.onceInitial
    }
}

extension PlayerItem {
    // MARK: 选择轨道 返回是否需要 seek

    func select(track: some FFmpegStreamAsset) -> Bool {
        if track.isEnabled {
            return false
        }

        // 遍历相同类型的轨道
        allAssetList.filter { $0.mediaType == track.mediaType }.forEach {
            // 使用track === $0判断是否是选择的轨道，返回布尔值
            // 使得只有选择的轨道是true
            $0.isEnabled = track === $0
        }

        // 如果是视频轨道，重新选择音频轨道
        if track.mediaType == .video {
            findBestAudio(videoTrack: track)
        }

        // 切换轨道后需要定位当前已播放的时间到新轨道
        seek(time: currentPlaybackTime) { _ in }

        return true
    }

    // MARK: 打开文件流读取数据

    private func openThread() {
        formatCtx?.pointee.interrupt_callback.opaque = nil
        formatCtx?.pointee.interrupt_callback.callback = nil

        avformat_close_input(&self.formatCtx)
        formatCtx = avformat_alloc_context()
        guard let formatCtx else {
            error = NSError(errorCode: .formatCreate)
            return
        }

        // 自定义AVIO中断回调
        var interruptCB = AVIOInterruptCB()
        interruptCB.opaque = Unmanaged.passUnretained(self).toOpaque()
        interruptCB.callback = { ctx -> Int32 in
            guard let ctx else {
                return 0
            }
            let formatContext = Unmanaged<PlayerItem>.fromOpaque(ctx).takeUnretainedValue()
            switch formatContext.sourceState {
            case .finished, .closed, .failed:
                return 1
            default:
                return 0
            }
        }

        formatCtx.pointee.interrupt_callback = interruptCB

        // 自定义协议
        ioContext = options.customFileProtocol
        if let ioContext {
            // 如果要自定义协议的话，那就用avio_alloc_context，对formatCtx.pointee.pb赋值
            formatCtx.pointee.pb = ioContext.getContext()
        }

        var urlString = ""
        if let nurl = url {
            if nurl.isFileURL {
                urlString = nurl.path
            } else {
                urlString = nurl.absoluteString
            }
        }

        // 打开输入流

        // TODO: 这里可以添加userAgent
        var avformatOptions = [String: Any]()
        avformatOptions["scan_all_pmts"] = 1
        // ts直播流需要加这个才能一直直播下去，不然播放一小段就会结束了。
        avformatOptions["reconnect"] = 1
        avformatOptions["reconnect_streamed"] = 1

        var avOptions = avformatOptions.avOptions
        var result = avformat_open_input(&self.formatCtx, urlString, nil, &avOptions)
        av_dict_free(&avOptions)

        if result == AVError.eof.code {
            sourceState = .finished
            controllerDelegate?.sourceDidFinished()
            return
        }

        guard result == 0 else {
            error = .init(errorCode: .formatOpenInput, avErrorCode: result)
            avformat_close_input(&self.formatCtx)
            return
        }

        // 查找流信息
        formatCtx.pointee.flags |= AVFMT_FLAG_GENPTS
        formatCtx.pointee.flags |= AVFMT_FLAG_NONBLOCK

        result = avformat_find_stream_info(formatCtx, nil)
        guard result == 0 else {
            error = .init(errorCode: .formatFindStreamInfo, avErrorCode: result)
            formatCtx.pointee.interrupt_callback.opaque = nil
            formatCtx.pointee.interrupt_callback.callback = nil
            avformat_close_input(&self.formatCtx)
            return
        }

        // 设置一些读取流的参数，比如
        formatCtx.pointee.pb?.pointee.eof_reached = 0
        let flags = formatCtx.pointee.iformat.pointee.flags
        maxFrameDuration = flags & AVFMT_TS_DISCONT == AVFMT_TS_DISCONT ? 10.0 : 3600.0

        // 获取格式名称
        let formatName = String(cString: formatCtx.pointee.iformat.pointee.name)

        // 判断是否需要按字节查找时间戳
        seekByBytes = (flags & AVFMT_NO_BYTE_SEEK == 0) && (flags & AVFMT_TS_DISCONT != 0) && formatName != "ogg"

        // 如果开始时间不是最小值，就设置开始时间
        if formatCtx.pointee.start_time != Int64.min {
            startTime = CMTime(value: formatCtx.pointee.start_time, timescale: AV_TIME_BASE)
            videoClock.time = startTime
            audioClock.time = startTime
        }
        // 获取时长
        duration = TimeInterval(max(formatCtx.pointee.duration, 0) / Int64(AV_TIME_BASE))
        // 获取流大小
        dynamicInfo.byteRate = formatCtx.pointee.bit_rate / 8
        fileSize = Double(dynamicInfo.byteRate) * duration

        // 创建解码器
        createCodec(formatCtx: formatCtx)

        // 获取章节信息
        if formatCtx.pointee.nb_chapters > 0 {
            chapters.removeAll()
            for i in 0 ..< formatCtx.pointee.nb_chapters {
                if let chapter = formatCtx.pointee.chapters[Int(i)]?.pointee {
                    let timeBase = Timebase(chapter.time_base)
                    let start = timeBase.cmtime(for: chapter.start).seconds
                    let end = timeBase.cmtime(for: chapter.end).seconds
                    let metadata = metadataToDictionary(chapter.metadata)
                    let title = metadata["title"] ?? ""
                    chapters.append(Chapter(start: start, end: end, title: title))
                }
            }
        }

        if videoTrackDecode == nil, audioTrackDecode == nil {
            sourceState = .failed
        } else {
            sourceState = .opened
            read()
        }
    }

    // MARK: 获取所有轨道并创建解码器

    private func createCodec(formatCtx: UnsafeMutablePointer<AVFormatContext>) {
        videoAdaptation = nil
        videoTrackDecode = nil
        audioTrackDecode = nil
        trackDecodeList.removeAll()
        allAssetList.removeAll()
        videoAudioBufferList.removeAll()

        // 获取所有资产
        for i in 0 ..< Int(formatCtx.pointee.nb_streams) {
            guard let coreStream = formatCtx.pointee.streams[i] else {
                continue
            }

            // 读取资产时丢弃所有帧数据
            coreStream.pointee.discard = AVDISCARD_ALL

            // 生成ffmpeg资产
            if let assetTrack = FFmpegStreamAsset(stream: coreStream) {
                if assetTrack.mediaType == .subtitle {
                    let subtitleDecode = TrackDecode<SubtitleFrame>(mediaType: .subtitle, frameQueueLength: 255, options: options)
                    assetTrack.subtitleTrackDecode = subtitleDecode
                    trackDecodeList.append(subtitleDecode)
                }
                allAssetList.append(assetTrack)
            } else if coreStream.pointee.codecpar.pointee.codec_type == AVMEDIA_TYPE_ATTACHMENT {
                // 把内嵌的字幕字体下载到临时文件夹并注册到系统
                if coreStream.pointee.codecpar.pointee.codec_id == AV_CODEC_ID_TTF || coreStream.pointee.codecpar.pointee.codec_id == AV_CODEC_ID_OTF {
                    let metadata = metadataToDictionary(coreStream.pointee.metadata)
                    if let filename = metadata["filename"], let extradata = coreStream.pointee.codecpar.pointee.extradata {
                        let extradataSize = coreStream.pointee.codecpar.pointee.extradata_size
                        let data = Data(bytes: extradata, count: Int(extradataSize))
                        var fontsDir = PlayerOptions.fontsDir
                        try? FileManager.default.createDirectory(at: fontsDir, withIntermediateDirectories: true)
                        fontsDir.appendPathComponent(filename)
                        _ = CTFontManagerRegisterFontsForURL(fontsDir as CFURL, .process, nil)
                        try? data.write(to: fontsDir)
                    }
                }
            }
        }

        // 获取视频流视频
        var videoStreamIndex: Int32 = -1
        videoStreamIndex = av_find_best_stream(formatCtx, AVMEDIA_TYPE_VIDEO, -1, -1, nil, 0)

        // 从所有资产中获取视频资产
        let videoTracks = allAssetList.filter { $0.mediaType == .video }

        // 获取视频轨道播放
        if let firstVideoAsset = videoTracks.first(where: { $0.streamIndex == videoStreamIndex }) {
            firstVideoAsset.isEnabled = true

            // 视频方向
            let rotation = firstVideoAsset.videoRotation

            // 根据方向更新分辨率
            naturalSize = abs(rotation - 90) <= 1 || abs(rotation - 270) <= 1 ? firstVideoAsset.naturalSize.reverse : firstVideoAsset.naturalSize

            // 去交错过滤器
            if options.videoDeInterlace {
                options.deInterlaceFilter(assetTrack: firstVideoAsset)
            }

            // 打印所有过滤器
            if !options.videoFilters.isEmpty {
                CinePlayerLog(level: .debug, "过滤器列表: ", options.videoFilters)
            }

            // 队列长度，规定缓存frame的数量
            let frameQueueLength = options.videoFrameMaxCount(fps: firstVideoAsset.nominalFrameRate)

            let itemTrack = TrackDecode<VideoVTBFrame>(mediaType: .video, frameQueueLength: frameQueueLength, options: options)

            // 设置代理，用于在itemTrack中访问playerItem的codecDidFinished方法
            itemTrack.playerItemDelegate = self

            trackDecodeList.append(itemTrack)

            videoTrackDecode = itemTrack

            if firstVideoAsset.codecpar.codec_id != AV_CODEC_ID_MJPEG {
                videoAudioBufferList.append(itemTrack)
            }

            let bitRates = videoTracks.map(\.bitRate).filter {
                $0 > 0
            }

            // 自适应比特率
            if bitRates.count > 1 {
                let bitRateState = VideoAdaptationState.BitRateState(bitRate: firstVideoAsset.bitRate, time: CACurrentMediaTime())
                videoAdaptation = VideoAdaptationState(bitRates: bitRates.sorted(by: <), duration: duration, fps: firstVideoAsset.nominalFrameRate, bitRateStates: [bitRateState])
            }
        }

        // 获取音频流播放
        let audioStreamIndex = av_find_best_stream(formatCtx, AVMEDIA_TYPE_AUDIO, -1, videoStreamIndex, nil, 0)
        let audioTracks = allAssetList.filter { $0.mediaType == .audio }

        if let firstAudioAsset = audioTracks.first(where: {
            audioStreamIndex > 0 ? $0.streamIndex == audioStreamIndex : true
        }), firstAudioAsset.codecpar.codec_id != AV_CODEC_ID_NONE {
            firstAudioAsset.isEnabled = true
            // 音频要比较所有的音轨，因为truehd的fps是1200，跟其他的音轨差距太大了
            let fps = audioTracks.map(\.nominalFrameRate).max() ?? 44
            // 队列长度，规定缓存frame的数量
            let frameQueueLength = options.audioFrameMaxCount(fps: fps, channelCount: Int(firstAudioAsset.audioDescriptor?.audioFormat.channelCount ?? 2))
            let itemTrack = TrackDecode<AudioFrame>(mediaType: .audio, frameQueueLength: frameQueueLength, options: options)

            // 设置代理，用于在itemTrack中访问playerItem的codecDidFinished方法
            itemTrack.playerItemDelegate = self
            trackDecodeList.append(itemTrack)
            audioTrackDecode = itemTrack
            videoAudioBufferList.append(itemTrack)
            isAudioStalled = false
        }
    }

    private func read() {
        readOperation = BlockOperation { [weak self] in
            guard let self else { return }
            Thread.current.name = (self.operationQueue.name ?? "") + "_read"
            Thread.current.stackSize = 65536

            self.readThread()
        }
        readOperation?.queuePriority = .veryHigh
        readOperation?.qualityOfService = .userInteractive
        if let readOperation {
            operationQueue.addOperation(readOperation)
        }
    }

    private func readThread() {
        if sourceState == .opened {
            // 设置起始播放时间，继续播放功能需要
            if options.startPlayTime > 0 {
                let timestamp = startTime + CMTime(seconds: options.startPlayTime)
                let flags = seekByBytes ? AVSEEK_FLAG_BYTE : 0
                let seekStartTime = CACurrentMediaTime()
                _ = avformat_seek_file(formatCtx, -1, Int64.min, timestamp.value, Int64.max, flags)
                audioClock.time = timestamp
                videoClock.time = timestamp
                CinePlayerLog(level: .debug, "start PlayTime: \(timestamp.seconds) spend Time: \(CACurrentMediaTime() - seekStartTime)")
            }
            sourceState = .reading
        }

        // 所有载入播放器的轨道开始解码
        trackDecodeList.forEach { $0.decode() }

        while [CineSourceState.paused, .seeking, .reading].contains(sourceState) {
            if sourceState == .paused {
                condition.wait()
            }

            if sourceState == .seeking {
                let seekToTime = seekTime
                let time = mainClock().time
                var increase = Int64(seekTime + startTime.seconds - time.seconds)
                var seekFlags = AVSEEK_FLAG_BACKWARD
                let timeStamp: Int64

                // 按字节查找时间戳
                if seekByBytes {
                    seekFlags |= AVSEEK_FLAG_BYTE
                    increase *= dynamicInfo.byteRate
                    var position = Int64(-1)
                    if position < 0 {
                        position = videoClock.position
                    }
                    if position < 0 {
                        position = audioClock.position
                    }
                    if position < 0 {
                        position = avio_tell(formatCtx?.pointee.pb)
                    }
                    timeStamp = position + increase
                } else {
                    increase *= Int64(AV_TIME_BASE)
                    timeStamp = Int64(time.seconds) * Int64(AV_TIME_BASE) + increase
                }

                let seekMin = increase > 0 ? timeStamp - increase + 2 : Int64.min
                let seekMax = increase < 0 ? timeStamp - increase - 2 : Int64.max

                let seekStartTime = CACurrentMediaTime()

                var result = avformat_seek_file(formatCtx, -1, seekMin, timeStamp, seekMax, seekFlags)

                if result < 0, (seekFlags & AVSEEK_FLAG_BACKWARD) == AVSEEK_FLAG_BACKWARD {
                    CinePlayerLog(level: .debug, "跳转失败, seekFlags 移除 BACKWARD")
                    seekFlags &= ~AVSEEK_FLAG_BACKWARD
                    result = avformat_seek_file(formatCtx, -1, seekMin, timeStamp, seekMax, seekFlags)
                }

                CinePlayerLog(level: .info, "跳转到 \(seekToTime), 用时: \(CACurrentMediaTime() - seekStartTime)")

                // 必须存在这个代码不然 ffmpeg vt 硬解会出现错误
                // 暂停 10ms 让 seek 完成
                usleep(10 * 1000)

                if sourceState == .closed {
                    break
                }

                if seekToTime != seekTime {
                    continue
                }

                isSeeking = true

                trackDecodeList.forEach { $0.seek(time: seekToTime) }

                codecDidChangeBuffer()

                audioClock.time = CMTime(seconds: seekToTime, preferredTimescale: time.timescale) + startTime
                videoClock.time = CMTime(seconds: seekToTime, preferredTimescale: time.timescale) + startTime

                DispatchQueue.main.async { [weak self] in
                    guard let self else { return }
                    self.seekingCompletionHandler?(result >= 0)
                    self.seekingCompletionHandler = nil
                }

                sourceState = .reading

            } else if sourceState == .reading {
                autoreleasepool {
                    _ = reading()
                }
            }
        }
    }

    // MARK: 读取包放入解码队列

    private func reading() -> Int32 {
        if sourceState == .closed {
            return 0
        }
        let packet = Packet()
        guard let corePacket = packet.corePacket else {
            return 0
        }
        let readResult = av_read_frame(formatCtx, corePacket)
        if readResult == 0 {
            if let outputFormatCtx, let formatCtx {
                let index = Int(corePacket.pointee.stream_index)

                if let outputIndex = streamMapping[index],
                   let inputTb = formatCtx.pointee.streams[index]?.pointee.time_base,
                   let outputTb = outputFormatCtx.pointee.streams[outputIndex]?.pointee.time_base,
                   let outputPacket
                {
                    av_packet_ref(outputPacket, corePacket)
                    outputPacket.pointee.stream_index = Int32(outputIndex)
                    av_packet_rescale_ts(outputPacket, inputTb, outputTb)
                    outputPacket.pointee.pos = -1
                    let ret = av_interleaved_write_frame(outputFormatCtx, outputPacket)
                    if ret < 0 {
                        CinePlayerLog(level: .debug, "can not av_interleaved_write_frame")
                    }
                }
            }
            if corePacket.pointee.size <= 0 {
                return 0
            }

            let first = allAssetList.first { $0.streamIndex == corePacket.pointee.stream_index }

            // MARK: 把数据包放入解码器的包队列

            // TODO: 需要针对字幕的处理
            // 因为现在字幕都是 isEnabled 的。导致每个轨道都解码
            // 使用了 ass 生成图片，会有很多内存占用，也需要在切换字幕的时候清理 libass 的 ass img 渲染器
            if let first, first.isEnabled {
                packet.assetTrack = first
                if first.mediaType == .video {
                    videoTrackDecode?.putPacket(packet: packet)
                } else if first.mediaType == .audio {
                    audioTrackDecode?.putPacket(packet: packet)
                } else if first.mediaType == .subtitle {
                    first.subtitleTrackDecode?.putPacket(packet: packet)
                }
            }
        } else {
            if readResult == AVError.eof.code || avio_feof(formatCtx?.pointee.pb) > 0 {
                trackDecodeList.forEach { $0.isEndOfFile = true }
                sourceState = .finished
            } else if readResult != AVError.tryAgain.code {
                //  if IS_AVERROR_INVALIDDATA(readResult)
                error = .init(errorCode: .readFrame, avErrorCode: readResult)
            }
        }
        return readResult
    }

    private func pause() {
        if sourceState == .reading {
            sourceState = .paused
        }
    }

    private func resume() {
        if sourceState == .paused {
            sourceState = .reading
            condition.signal()
        }
    }
}

public extension PlayerItem {
    internal var seekable: Bool {
        guard let formatCtx else {
            return false
        }
        var seekable = true
        if let ioContext = formatCtx.pointee.pb {
            seekable = ioContext.pointee.seekable > 0 || duration != 0
        }
        return seekable
    }

    // MARK: 音视频同步

    func videoClockSync(main: PlayerClock, nextVideoTime: TimeInterval, fps: Double, frameCount: Int) -> (Double, ClockProcessType) {
        let desire = main.getTime() - options.videoDelay // 计算期望时间
        let diff = nextVideoTime - desire // 计算时间差
        if diff >= 1 / fps / 2 {
            videoClockDelayCount = 0
            return (diff, .remain) // 保持状态
        } else {
            if diff < -4 / fps {
                videoClockDelayCount += 1
                let log = "[video] video delay=\(diff), clock=\(desire), delay count=\(videoClockDelayCount), frameCount=\(frameCount)"
                if frameCount == 1 {
                    if diff < -1, videoClockDelayCount % 10 == 0 {
                        CinePlayerLog(level: .debug, "\(log) drop gop Packet") // 丢弃GOP数据包
                        return (diff, .dropGOPPacket)
                    } else if videoClockDelayCount % 5 == 0 {
                        CinePlayerLog(level: .debug, "\(log) drop next frame") // 丢弃下一帧
                        return (diff, .dropNextFrame)
                    } else {
                        return (diff, .next) // 下一步
                    }
                } else {
                    if diff < -8, videoClockDelayCount % 100 == 0 {
                        CinePlayerLog(level: .debug, "\(log) seek video track") // 寻找视频轨道
                        return (diff, .seek)
                    }
                    if diff < -1, videoClockDelayCount % 10 == 0 {
                        CinePlayerLog(level: .debug, "\(log) flush video track") // 刷新视频轨道
                        return (diff, .flush)
                    }
                    if videoClockDelayCount % 2 == 0 {
                        CinePlayerLog(level: .debug, "\(log) drop next frame") // 丢弃下一帧
                        return (diff, .dropNextFrame)
                    } else {
                        return (diff, .next) // 下一步
                    }
                }
            } else {
                videoClockDelayCount = 0
                return (diff, .next) // 下一步
            }
        }
    }

    // MARK: 自适应视频比特率

    func adaptableBitRate(state: VideoAdaptationState?) -> (Int64, Int64)? {
        guard let state, let last = state.bitRateStates.last, CACurrentMediaTime() - last.time > options.maxBufferDuration / 2, let index = state.bitRates.firstIndex(of: last.bitRate) else {
            return nil
        }
        let isUp = state.loadedCount > Int(Double(state.fps) * options.maxBufferDuration / 2) // 判断是否上升
        if isUp != state.isPlayable {
            return nil
        }
        if isUp {
            if index < state.bitRates.endIndex - 1 {
                return (last.bitRate, state.bitRates[index + 1]) // 返回上一个比特率
            }
        } else {
            if index > state.bitRates.startIndex {
                return (last.bitRate, state.bitRates[index - 1]) // 返回下一个比特率
            }
        }
        return nil
    }

    // MARK: 处理缓冲并返回是否可播放状态

    func playable(buffers: [BufferProtocol], isFirst _: Bool, isSeek: Bool) -> ItemLoadingState {
        let packetCount = buffers.map(\.packetCount).min() ?? 0 // 获取最小数据包数
        let frameCount = buffers.map(\.frameCount).min() ?? 0 // 获取最小帧数
        let isEndOfFile = buffers.allSatisfy(\.isEndOfFile) // 检查是否到达文件末尾
        let loadedTime = buffers.map(\.loadedTime).min() ?? 0 // 获取最小加载时间
        let progress = options.preferredForwardBufferDuration == 0 ? 100 : loadedTime * 100.0 / options.preferredForwardBufferDuration // 计算进度百分比

        // 检查是否可播放
        let isPlayable = buffers.allSatisfy { capacity in
            if capacity.isEndOfFile && capacity.packetCount == 0 {
                return true
            }
            guard capacity.frameCount >= 2 else {
                return false
            }
            if capacity.isEndOfFile {
                return true
            }
            return capacity.loadedTime >= self.options.preferredForwardBufferDuration
        }

//        CinePlayerLog(level: .debug, "isPlayable: \(isPlayable), isEndOfFile: \(isEndOfFile), loadedTime: \(loadedTime), progress: \(progress), packetCount: \(packetCount), frameCount: \(frameCount), isSeek: \(isSeek)")

        // 返回加载状态
        return ItemLoadingState(loadedTime: loadedTime, progress: progress, packetCount: packetCount,
                                frameCount: frameCount, isEndOfFile: isEndOfFile, isPlayable: isPlayable, isSeek: isSeek)
    }

    func prepareToPlay() {
        guard [CineSourceState.idle, .closed, .failed].contains(sourceState) else {
            return
        }
        sourceState = .opening
        openOperation = BlockOperation { [weak self] in
            guard let self else { return }
            Thread.current.name = (self.operationQueue.name ?? "") + "_open"
            Thread.current.stackSize = 65536

            self.openThread()
        }
        openOperation?.queuePriority = .veryHigh
        openOperation?.qualityOfService = .userInteractive
        if let openOperation {
            operationQueue.addOperation(openOperation)
        }
    }

    // MARK: SEEK 跳转到时间戳播放

    func seek(time: TimeInterval, completion: @escaping ((Bool) -> Void)) {
        if sourceState == .reading || sourceState == .paused {
            seekingCompletionHandler = completion
            seekTime = time
            sourceState = .seeking
            condition.broadcast()
        } else if sourceState == .finished {
            seekingCompletionHandler = completion
            seekTime = time
            sourceState = .seeking
            read()
        } else if sourceState == .seeking {
            seekingCompletionHandler = completion
            seekTime = time
        }
        isAudioStalled = audioTrackDecode == nil
    }

    // MARK: Shutdown 释放资源

    func shutdown() {
        controllerDelegate = nil

        guard sourceState != .closed else { return }

        sourceState = .closed

        av_packet_free(&outputPacket)

        let closeOperation = BlockOperation {
            Thread.current.name = (self.operationQueue.name ?? "") + "_close"

            Task { @MainActor in
                self.audioTrackDecode?.shutdown()
                self.videoTrackDecode?.shutdown()
                self.trackDecodeList.forEach { $0.shutdown() }

                CinePlayerLog(level: .info, "释放资源")
                // 自定义的协议才会av_class为空
                if let formatCtx = self.formatCtx, (formatCtx.pointee.flags & AVFMT_FLAG_CUSTOM_IO) != 0, let opaque = formatCtx.pointee.pb.pointee.opaque {
                    let value = Unmanaged<CustomAVIOContext>.fromOpaque(opaque).takeRetainedValue()
                    value.close()
                }
                // 不要自己来释放pb。不然第二次播放同一个url会出问题
                // self.formatCtx?.pointee.pb = nil
                self.formatCtx?.pointee.interrupt_callback.opaque = nil
                self.formatCtx?.pointee.interrupt_callback.callback = nil
                avformat_close_input(&self.formatCtx)
                avformat_close_input(&self.outputFormatCtx)
                self.duration = 0
                self.closeOperation = nil
                self.operationQueue.cancelAllOperations()
            }
        }

        closeOperation.queuePriority = .veryHigh
        closeOperation.qualityOfService = .userInteractive
        if let readOperation {
            readOperation.cancel()
            closeOperation.addDependency(readOperation)
        } else if let openOperation {
            openOperation.cancel()
            closeOperation.addDependency(openOperation)
        }
        operationQueue.addOperation(closeOperation)
        condition.signal()
        self.closeOperation = closeOperation
    }
}

extension PlayerItem {
    /// 缓冲发生变化时调用
    func codecDidChangeBuffer() {
        // 获取当前的加载状态
        let loadingState = playable(buffers: videoAudioBufferList, isFirst: isFirst, isSeek: isSeeking)
        // 通知控制器代理加载状态发生变化
        controllerDelegate?.sourceDidChange(itemloadingState: loadingState)
        // 如果加载状态可播放
        if loadingState.isPlayable {
            isFirst = false
            isSeeking = false
            // 如果加载能播放时间超过最大缓冲持续时间
            if loadingState.loadedTime > options.maxBufferDuration {
                adaptableVideo(loadingState: loadingState)
                // 暂停加载
                pause()
            } else if loadingState.loadedTime < options.maxBufferDuration / 2 {
                // 继续加载
                resume()
            }
        } else {
            // 继续加载
            resume()
            adaptableVideo(loadingState: loadingState)
        }
    }

    /// 自适应视频比特率处理
    private func adaptableVideo(loadingState: ItemLoadingState) {
        // 如果没有视频适配状态或已结束或在寻找或状态为寻找，则返回
        if videoAdaptation == nil || loadingState.isEndOfFile || loadingState.isSeek || sourceState == .seeking {
            return
        }
        guard let track = videoTrackDecode else {
            return
        }

        // 更新视频适配状态
        videoAdaptation?.loadedCount = track.packetCount + track.frameCount
        videoAdaptation?.currentPlaybackTime = currentPlaybackTime
        videoAdaptation?.isPlayable = loadingState.isPlayable
        // 获取新的比特率并进行适配
        guard let (oldBitRate, newBitrate) = adaptableBitRate(state: videoAdaptation), oldBitRate != newBitrate,
              let newFFmpegStreamAsset = allAssetList.first(where: { $0.mediaType == .video && $0.bitRate == newBitrate })
        else {
            return
        }

        // 禁用旧的比特率轨道，启用新的比特率轨道
        allAssetList.first { $0.mediaType == .video && $0.bitRate == oldBitRate }?.isEnabled = false
        newFFmpegStreamAsset.isEnabled = true

        findBestAudio(videoTrack: newFFmpegStreamAsset)
        // 更新比特率状态
        let bitRateState = VideoAdaptationState.BitRateState(bitRate: newBitrate, time: CACurrentMediaTime())
        videoAdaptation?.bitRateStates.append(bitRateState)
    }

    /// 查找最佳音频轨道
    private func findBestAudio(videoTrack: FFmpegStreamAsset) {
        guard videoAdaptation != nil, let first = allAssetList.first(where: { $0.mediaType == .audio && $0.isEnabled }) else {
            return
        }
        // 查找最佳音频流
        let index = av_find_best_stream(formatCtx, AVMEDIA_TYPE_AUDIO, -1, videoTrack.streamIndex, nil, 0)
        if index != first.streamIndex {
            first.isEnabled = false
            allAssetList.first { $0.mediaType == .audio && $0.streamIndex == index }?.isEnabled = true
        }
    }
}

func metadataToDictionary(_ native: OpaquePointer?) -> [String: String] {
    var dict = [String: String]()
    if let native {
        var prev: UnsafeMutablePointer<AVDictionaryEntry>?
        while let tag = av_dict_get(native, "", prev, AV_DICT_IGNORE_SUFFIX) {
            dict[String(cString: tag.pointee.key)] = String(cString: tag.pointee.value)
            prev = tag
        }
    }
    return dict
}
