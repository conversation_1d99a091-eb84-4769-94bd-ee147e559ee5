//
//  PlayerItemDelegate.swift
//  VideoPlayer
//
//  Created by Zero on 2024/10/9.
//

import AVFoundation

/// 定义一个协议 表示 PlayerItem 的代理 用于 TrackDecode 中调用
@MainActor
protocol PlayerItemDelegate: AnyObject {
    func codecDidFinished(track: some BufferProtocol) // 编解码完成
}

extension PlayerItem: PlayerItemDelegate {
    /// 渲染帧队列为空时会调用
    func codecDidFinished(track: some BufferProtocol) {
        // 如果轨道是音频
        if track.mediaType == .audio {
            isAudioStalled = true
        }
        // 检查所有轨道是否都已结束
        let allSatisfy = videoAudioBufferList.allSatisfy { $0.isEndOfFile && $0.frameCount == 0 && $0.packetCount == 0 }
        if allSatisfy {
            controllerDelegate?.sourceDidFinished()
            // 暂停缓冲 暂停缓冲
            bufferTimer.fireDate = Date.distantFuture
        }
    }
}

// 用于 videoView 中调用的代理

extension PlayerItem: OutputRenderSourceDelegate {
    /// 获取主时钟，优先使用音频时钟
    func mainClock() -> PlayerClock {
        isAudioStalled ? videoClock : audioClock
    }

    /// 设置视频时间和位置
    /// 在 videoView 中调用
    public func setVideo(time: CMTime, position: Int64) {
        videoClock.time = time
        videoClock.position = position
        videoDisplayCount += 1

        let diff = videoClock.lastMediaTime - lastVideoClock.lastMediaTime

        // 统计实时码率
        if diff > 2 {
            let timeDiff = (videoClock.time - lastVideoClock.time).seconds
            if timeDiff != 0 {
                dynamicInfo.byteRate = Int64(Double(videoClock.position - lastVideoClock.position) / timeDiff)
            }
            dynamicInfo.displayFPS = Double(videoDisplayCount) / diff
            videoDisplayCount = 0
            lastVideoClock = videoClock
        }
    }

    /// 获取视频帧，用于 videoView 的渲染器
    public func getVideoOutputRender(force: Bool) -> VideoVTBFrame? {
        // 确保 videoTrack 存在
        guard let videoTrackDecode else {
            return nil
        }

        // 根据 force 参数确定处理类型
        var type: ClockProcessType = force ? .next : .remain

        // 用于筛选视频帧
        let predicate: ((VideoVTBFrame, Int) -> Bool)? = force ? nil : { [weak self] frame, count -> Bool in
            guard let self else { return true }
            (self.dynamicInfo.audioVideoSyncDiff, type) = self.videoClockSync(main: self.mainClock(), nextVideoTime: frame.seconds, fps: Double(frame.fps), frameCount: count)
            return type != .remain
        }

        // 获取视频帧
        let frame = videoTrackDecode.getOutputRender(where: predicate)

        // 根据处理类型进行相应操作
        switch type {
        case .remain:
            break

        case .next:
            break

        case .dropNextFrame:
            // 丢弃下一帧
            if videoTrackDecode.getOutputRender(where: nil) != nil {
                dynamicInfo.droppedVideoFrameCount += 1
            }

        case .flush:
            // 刷新队列
            let count = videoTrackDecode.outputRenderQueue.count
            videoTrackDecode.outputRenderQueue.flush()
            dynamicInfo.droppedVideoFrameCount += UInt32(count)

        case .seek:
            // 寻找特定时间
            videoTrackDecode.outputRenderQueue.flush()
            videoTrackDecode.seekTime = mainClock().time.seconds

        case .dropNextPacket:
            // 丢弃下一个数据包
            if let videoTrack = videoTrackDecode as? TrackDecode {
                let packet = videoTrack.packetQueue.dequeue { item, _ -> Bool in
                    !item.isKeyFrame
                }
                if packet != nil {
                    dynamicInfo.droppedVideoPacketCount += 1
                }
            }

        case .dropGOPPacket:
            // 丢弃一组图片（GOP）数据包
            if let videoTrack = videoTrackDecode as? TrackDecode {
                var packet: Packet? = nil
                repeat {
                    packet = videoTrack.packetQueue.dequeue { item, _ -> Bool in
                        !item.isKeyFrame
                    }
                    if packet != nil {
                        dynamicInfo.droppedVideoPacketCount += 1
                    }
                } while packet != nil
            }
        }

        // 返回获取的视频帧
        return frame
    }

    /// 设置音频时间和位置
    public func setAudio(time: CMTime, position: Int64) {
        // 主线程播放起来会更顺滑
        Task { @MainActor in
            audioClock.time = time
            audioClock.position = position
        }
    }

    /// 获取音频输出渲染
    public func getAudioOutputRender() -> AudioFrame? {
        if let frame = audioTrackDecode?.getOutputRender(where: nil) {
            return frame
        } else {
            return nil
        }
    }
}
