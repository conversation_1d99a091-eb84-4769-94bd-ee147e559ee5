import AVFoundation
import CoreMedia

// 存储帧的队列，解码完成后放入队列。再取出给渲染器，渲染为音频或视频

/// 队列元素
public protocol FrameQueueItem {
    var timebase: Timebase { get }
    var timestamp: Int64 { get set }
    var duration: Int64 { get set }
    // byte position
    var position: Int64 { get set }
    var size: Int32 { get set }
}

extension FrameQueueItem {
    var seconds: TimeInterval { cmtime.seconds }
    var cmtime: CMTime { timebase.cmtime(for: timestamp) }
}

/// 这个是单生产者，多消费者的阻塞队列和单生产者，多消费者的阻塞环形队列。并且环形队列还要有排序的能力。
/// 用于存储解码后的帧数据
/// 队列是个泛型以适配音频或视频帧
public class FrameQueue<Item: FrameQueueItem> {
    private var _buffer = ContiguousArray<Item?>() // 环形缓冲区
    private let condition = NSCondition() // 条件变量，用于线程同步
    private var headIndex = UInt(0) // 头索引
    private var tailIndex = UInt(0) // 尾索引
    private let expanding: Bool // 是否允许扩展容量
    private let sorted: Bool // 是否需要排序

    private var destroyed = false // 是否已销毁
    @inline(__always)
    private var _count: Int { Int(tailIndex &- headIndex) } // 当前元素数量
    @inline(__always)
    public var count: Int { _count } // 公共接口，获取当前元素数量

    public internal(set) var fps: Float = 24 // 帧率
    public private(set) var maxCount: Int // 最大容量
    private var mask: UInt // 掩码，用于计算索引

    // MARK: 初始化

    /// - Parameters:
    ///   - initialCapacity: 初始容量
    ///   - sorted: 是否需要排序
    ///   - expanding: 是否允许扩展容量
    public init(length: Int = 256, sorted: Bool = false, expanding: Bool = true) {
        self.expanding = expanding
        self.sorted = sorted

        let capacity = length.nextPowerOf2() // 计算下一个2的幂
        _buffer = ContiguousArray<Item?>(repeating: nil, count: Int(capacity)) // 初始化缓冲区
        maxCount = Int(capacity)
        mask = UInt(maxCount - 1)
        assert(_buffer.count == capacity)
    }

    // MARK: 入队

    /// 向缓冲区推入一个元素
    /// - Parameter value: 要推入的元素
    public func enqueue(_ value: Item) {
        condition.lock() // 加锁
        defer { condition.unlock() } // 解锁
        guard !destroyed else { return } // 如果已销毁，直接返回

        _buffer[Int(tailIndex & mask)] = value // 插入元素

        if sorted {
            // 插入排序
            var index = tailIndex
            while index > headIndex {
                guard let item = _buffer[Int((index - 1) & mask)] else {
                    break
                }
                if item.timestamp <= _buffer[Int(index & mask)]!.timestamp {
                    break
                }
                _buffer.swapAt(Int((index - 1) & mask), Int(index & mask))
                index -= 1
            }
        }

        tailIndex &+= 1 // 更新尾索引

        if _count >= maxCount {
            if expanding {
                _doubleCapacity() // 扩展容量
            } else {
                condition.wait() // 等待
            }
        } else if _count == 1 {
            condition.signal() // 唤醒等待的线程
        }
    }

    // MARK: 出队

    /// 从缓冲区弹出一个元素
    /// - Parameters:
    ///   - wait: 是否等待
    ///   - predicate: 过滤条件
    /// - Returns: 弹出的元素
    public func dequeue(wait: Bool = false, where predicate: ((Item, Int) -> Bool)? = nil) -> Item? {
        condition.lock() // 加锁
        defer { condition.unlock() } // 解锁
        guard !destroyed else { return nil } // 如果已销毁，直接返回

        if headIndex == tailIndex {
            if wait {
                condition.wait() // 等待
                guard !destroyed, headIndex != tailIndex else { return nil }
            } else {
                return nil
            }
        }

        let index = Int(headIndex & mask)
        guard let item = _buffer[index] else {
            return nil
        }

        if let predicate, !predicate(item, _count) {
            return nil
        } else {
            headIndex &+= 1 // 更新头索引
            _buffer[index] = nil

            if _count == maxCount >> 1 {
                condition.signal() // 唤醒等待的线程
            }
            return item
        }
    }

    // MARK: 搜索

    /// - Parameter predicate: 过滤条件
    /// - Returns: 满足条件的元素数组
    public func search(where predicate: (Item) -> Bool) -> [Item] {
        condition.lock() // 加锁
        defer { condition.unlock() } // 解锁
        var result = [Item]()
        var i = headIndex

        while i < tailIndex {
            if let item = _buffer[Int(i & mask)], predicate(item) {
                result.append(item)
                _buffer[Int(i & mask)] = nil
                headIndex = i + 1
            } else {
                return result
            }
            i += 1
        }
        return result
    }

    // MARK: 跳转

    public func seek(seconds: Double, needKeyFrame: Bool = false) -> UInt? {
        condition.lock()
        defer { condition.unlock() }
        var i = headIndex
        if let item = _buffer[Int(i & mask)] {
            let isForward = seconds > item.seconds
            if isForward {
                i += 1
                while i <= tailIndex, let item = _buffer[Int(i & mask)] {
                    if item.seconds >= seconds {
                        if needKeyFrame {
                            if let packet = item as? Packet, packet.isKeyFrame {
                                return i
                            } else {
                                i -= 1
                                while i > headIndex {
                                    if let packet = _buffer[Int(i & mask)] as? Packet, packet.isKeyFrame {
                                        return i
                                    }
                                    i -= 1
                                }
                                return nil
                            }
                        } else {
                            return i
                        }
                    }
                    i += 1
                }
            } else {
                while i > 0, let item = _buffer[Int(i & mask)] {
                    if item.seconds <= seconds {
                        if needKeyFrame {
                            if let packet = item as? Packet, packet.isKeyFrame {
                                return i
                            }
                        } else {
                            return i
                        }
                    }
                    i -= 1
                }
            }
        }
        return nil
    }

    // MARK: 更新

    func update(headIndex: UInt) {
        condition.lock()
        defer { condition.unlock() }
        self.headIndex = headIndex
    }

    // MARK: 清空缓冲区

    public func flush() {
        condition.lock() // 加锁
        defer { condition.unlock() } // 解锁
        headIndex = 0
        tailIndex = 0
        _buffer.removeAll(keepingCapacity: !destroyed) // 清空缓冲区
        _buffer.append(contentsOf: ContiguousArray<Item?>(repeating: nil, count: destroyed ? 1 : maxCount))
        condition.broadcast() // 广播通知所有等待的线程
    }

    // MARK: 销毁缓冲区

    public func shutdown() {
        destroyed = true
        maxCount = 0
        flush() // 清空缓冲区
    }

    // MARK: 扩展缓冲区容量

    private func _doubleCapacity() {
        var newBacking: ContiguousArray<Item?> = []
        let newCapacity = maxCount << 1 // 扩展为原来的两倍
        precondition(newCapacity > 0, "Can't double capacity of \(_buffer.count)")
        assert(newCapacity % 2 == 0)
        newBacking.reserveCapacity(newCapacity)

        let head = Int(headIndex & mask)
        newBacking.append(contentsOf: _buffer[head ..< maxCount])
        if head > 0 {
            newBacking.append(contentsOf: _buffer[0 ..< head])
        }

        let repeatitionCount = newCapacity &- newBacking.count
        newBacking.append(contentsOf: repeatElement(nil, count: repeatitionCount))

        headIndex = 0
        tailIndex = UInt(newBacking.count &- repeatitionCount)
        _buffer = newBacking
        maxCount = newCapacity
        mask = UInt(maxCount - 1)
    }
}

extension FixedWidthInteger {
    /// 返回下一个2的幂
    @inline(__always)
    func nextPowerOf2() -> Self {
        guard self != 0 else { return 1 }
        return 1 << (Self.bitWidth - (self - 1).leadingZeroBitCount)
    }
}
