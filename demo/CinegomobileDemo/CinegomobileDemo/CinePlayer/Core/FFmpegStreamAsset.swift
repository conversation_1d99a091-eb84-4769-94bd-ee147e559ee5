import AVFoundation
import CFFmpeg
import VideoToolbox

/// 使用 FFmpeg 解析媒体流里的资源
public class FFmpegStreamAsset: AnyObject, CustomStringConvertible {
    /// 轨道ID
    public private(set) var streamIndex: Int32 = 0
    /// 编解码器名称，用于显示
    public let codecName: String
    /// 轨道名称
    ///    public var name: String = ""
    /// 语言代码
    public private(set) var languageCode: String?
    /// 标称帧率
    public var nominalFrameRate: Float = 0
    /// 平均帧率
    public private(set) var avgFrameRate = Timebase.defaultValue
    /// 实际帧率
    public private(set) var realFrameRate = Timebase.defaultValue
    /// 比特率
    public private(set) var bitRate: Int64 = 0
    /// 媒体类型
    public let mediaType: AVFoundation.AVMediaType
    /// 格式名称
    public let formatName: String?
    /// 位深度
    public var bitDepth: Int32
    public var delay: TimeInterval = 0
    /// 流指针
    private var stream: UnsafeMutablePointer<AVStream>?
    /// 开始时间
    var startTime = CMTime.zero
    /// 编解码器参数
    var codecpar: AVCodecParameters
    /// 时间基
    var timebase: Timebase = .defaultValue
    /// 每个原始样本的位数
    let bitsPerRawSample: Int32
    /// 音频描述符
    public let audioDescriptor: AudioDescriptor?
    /// 视频旋转角度
    public private(set) var videoRotation: CGFloat = 0
    /// Dolby Vision 配置
    public var dolbyVisionConfig: DOVIDecoderConfigurationRecord?
    /// 字段顺序
    public let fieldOrder: FFmpegFieldOrder
    /// 格式描述
    public let formatDescription: CMFormatDescription?
    /// 比特流过滤器
    var bitStreamFilter: BitStreamFilter.Type?

    /// 像素格式类型
    public var pixelFormatType: OSType? {
        let format = AVPixelFormat(codecpar.format)
        return format.osType(fullRange: formatDescription?.fullRangeVideo ?? false)
    }

    // subtitle
    public let isImageSubtitle: Bool
    var assImageRenderer: CineSubtitleProtocol?
    /// 字幕轨道解码器
    var subtitleTrackDecode: TrackDecode<SubtitleFrame>?

    /// 描述信息
    public var description: String = ""
    func genDescription() {
        if let language {
            description = "[\(language)]"
        }
        if mediaType != .subtitle {
            description += " \(codecName)"
            if let formatName {
                description += ", \(formatName)"
            }

            if bitsPerRawSample > 0 {
                description += "(\(bitsPerRawSample.kmFormatted) bit)"
            }
            if let audioDescriptor {
                description += ", \(audioDescriptor.sampleRate)Hz"
                description += ", \(audioDescriptor.channel.description)"
            }
            if let formatDescription {
                if mediaType == .video {
                    let naturalSize = formatDescription.naturalSize
                    description += ", \(Int(naturalSize.width))x\(Int(naturalSize.height))"
                    description += String(format: ", %.2f fps", nominalFrameRate)
                }
            }
            if bitRate > 0 {
                description += ", \(bitRate.kmFormatted)bps"
            }
        }
    }

    /// 便利初始化方法
    convenience init?(stream: UnsafeMutablePointer<AVStream>) {
        let codecpar = stream.pointee.codecpar.pointee

        self.init(codecpar: codecpar)
        self.stream = stream

        streamIndex = stream.pointee.index

        let metadata = metadataToDictionary(stream.pointee.metadata)

        if let value = metadata["variant_bitrate"] ?? metadata["BPS"], let bitRate = Int64(value) {
            self.bitRate = bitRate
        }

        var timebase = Timebase(stream.pointee.time_base)
        if timebase.num <= 0 || timebase.den <= 0 {
            timebase = Timebase(num: 1, den: 1000)
        }
        if stream.pointee.start_time != Int64.min {
            startTime = timebase.cmtime(for: stream.pointee.start_time)
        }

        self.timebase = timebase

        avgFrameRate = Timebase(stream.pointee.avg_frame_rate)
        realFrameRate = Timebase(stream.pointee.r_frame_rate)

        if mediaType == .audio {
            var frameSize = codecpar.frame_size
            if frameSize < 1 {
                frameSize = timebase.den / timebase.num
            }
            nominalFrameRate = max(Float(codecpar.sample_rate / frameSize), 48)
        } else {
            if stream.pointee.duration > 0, stream.pointee.nb_frames > 0, stream.pointee.nb_frames != stream.pointee.duration {
                nominalFrameRate = Float(stream.pointee.nb_frames) * Float(timebase.den) / Float(stream.pointee.duration) * Float(timebase.num)
            } else if avgFrameRate.den > 0, avgFrameRate.num > 0 {
                nominalFrameRate = avgFrameRate.rational.float
            } else {
                nominalFrameRate = 24
            }
        }

        if let value = metadata["language"], value != "und" {
            languageCode = value
        } else {
            languageCode = nil
        }

        genDescription()
        if let value = metadata["title"] {
            description += " - \(value)"
        }
    }

    /// 初始化方法
    init?(codecpar: AVCodecParameters) {
        self.codecpar = codecpar
        bitRate = codecpar.bit_rate

        let format = AVPixelFormat(rawValue: codecpar.format)
        bitDepth = format.bitDepth

        let codecType = codecpar.codec_id.mediaSubType

        var codecName = ""
        if let descriptor = avcodec_descriptor_get(codecpar.codec_id) {
            codecName += String(cString: descriptor.pointee.name)
        }

        if codecpar.codec_type == AVMEDIA_TYPE_VIDEO || codecpar.codec_type == AVMEDIA_TYPE_AUDIO {
            // 配置信息
            // Dolby Digital Plus + Dolby Atmos
            // Main 10
            if codecpar.profile != 0 && codecpar.codec_id != AV_CODEC_ID_NONE {
                if let profileName = avcodec_profile_name(codecpar.codec_id, codecpar.profile) {
                    codecName += " (\(String(cString: profileName)))"
                }
            }
        }

        self.codecName = codecName

        fieldOrder = FFmpegFieldOrder(rawValue: UInt8(codecpar.field_order.rawValue)) ?? .unknown

        var formatDescriptionOut: CMFormatDescription?

        if codecpar.codec_type == AVMEDIA_TYPE_AUDIO {
            mediaType = .audio
            audioDescriptor = AudioDescriptor(codecpar: codecpar)

            bitDepth = 0
            let layout = codecpar.ch_layout
            let channelsPerFrame = UInt32(layout.nb_channels)
            let sampleFormat = AVSampleFormat(codecpar.format)
            let bytesPerSample = UInt32(av_get_bytes_per_sample(sampleFormat))

            let formatFlags = ((sampleFormat == AV_SAMPLE_FMT_FLT || sampleFormat == AV_SAMPLE_FMT_DBL) ? kAudioFormatFlagIsFloat : sampleFormat == AV_SAMPLE_FMT_U8 ? 0 : kAudioFormatFlagIsSignedInteger) | kAudioFormatFlagIsPacked

            var audioStreamBasicDescription = AudioStreamBasicDescription(mSampleRate: Float64(codecpar.sample_rate), mFormatID: codecType.rawValue, mFormatFlags: formatFlags, mBytesPerPacket: bytesPerSample * channelsPerFrame, mFramesPerPacket: 1, mBytesPerFrame: bytesPerSample * channelsPerFrame, mChannelsPerFrame: channelsPerFrame, mBitsPerChannel: bytesPerSample * 8, mReserved: 0)

            _ = CMAudioFormatDescriptionCreate(allocator: kCFAllocatorDefault, asbd: &audioStreamBasicDescription, layoutSize: 0, layout: nil, magicCookieSize: 0, magicCookie: nil, extensions: nil, formatDescriptionOut: &formatDescriptionOut)

            if let name = av_get_sample_fmt_name(sampleFormat) {
                formatName = String(cString: name)
            } else {
                formatName = nil
            }
        } else if codecpar.codec_type == AVMEDIA_TYPE_VIDEO {
            audioDescriptor = nil
            mediaType = .video

            // 获取杜比视界配置信息
            if let doviSideData = av_packet_side_data_get(codecpar.coded_side_data, codecpar.nb_coded_side_data, AV_PKT_DATA_DOVI_CONF) {
                dolbyVisionConfig = doviSideData.pointee.data.withMemoryRebound(to: DOVIDecoderConfigurationRecord.self, capacity: 1) { $0 }.pointee

                let dolbyVisionConfigStr = getDolbyVisionConfigStr(codecID: codecpar.codec_id, bitDepth: bitDepth, dolbyVisionConfig: dolbyVisionConfig)

                print("DOVIDecoderConfigurationRecord: \(dolbyVisionConfigStr)")
            }

            // 获取旋转角度
            if let displayMatrixSideData = av_packet_side_data_get(codecpar.coded_side_data, codecpar.nb_coded_side_data, AV_PKT_DATA_DISPLAYMATRIX) {
                let matrix = displayMatrixSideData.pointee.data.withMemoryRebound(to: Int32.self, capacity: 1) { $0 }
                videoRotation = CGFloat(Int(-av_display_rotation_get(matrix)) % 360)
            }

            var extradataSize = Int32(0)
            let extradata = codecpar.extradata
            var atomsData: Data?

            // ConvertNAL
            // https://github.com/xbmc/xbmc/blob/master/xbmc/utils/BitstreamConverter.cpp#L368
            // CBitstreamConverter::Open 部分
            if let extradata {
                extradataSize = codecpar.extradata_size
                if extradata[0] == 1 {
                    atomsData = Data(bytes: extradata, count: Int(extradataSize))
                    if extradataSize >= 5, extradata[4] == 0xFE {
                        extradata[4] = 0xFF
                        bitStreamFilter = Nal3ToNal4BitStreamFilter.self
                    }
                } else {
                    // 支持Annex-B硬解
                    var ioContext: UnsafeMutablePointer<AVIOContext>?
                    guard avio_open_dyn_buf(&ioContext) == 0 else {
                        return nil
                    }
                    var extra = codecpar.extradata
                    if codecpar.codec_id == AV_CODEC_ID_HEVC {
                        ff_isom_write_hvcc(ioContext, extra, extradataSize, 0)
                    } else if codecpar.codec_id == AV_CODEC_ID_AV1 {
                        ff_isom_write_av1c(ioContext, extra, extradataSize, 1)
                    } else if codecpar.codec_id == AV_CODEC_ID_VP9 {
                        ff_isom_write_vpcc(nil, ioContext, extra, extradataSize, &self.codecpar)
                    } else {
                        ff_isom_write_avcc(ioContext, extra, extradataSize)
                    }
                    extradataSize = avio_close_dyn_buf(ioContext, &extra)
                    guard let extradata = extra else {
                        return nil
                    }
                    atomsData = Data(bytes: extradata, count: Int(extradataSize))
                    if codecpar.codec_id != AV_CODEC_ID_AV1 {
                        bitStreamFilter = AnnexbToCCBitStreamFilter.self
                    }
                }
            } else {
                if codecType.rawValue == kCMVideoCodecType_VP9 {
                    atomsData = createVP9CodecConfigurationBoxData(codecpar: codecpar)
                } else {
                    atomsData = nil
                }
            }

            // 创建基础格式描述
            let extensions = createVTFormatExtensions(format: format, codecpar: codecpar)

            // 封装盒子处理
            let boxName = codecType.rawValue.configurationBox
            if let atomsData {
                extensions[kCMFormatDescriptionExtension_SampleDescriptionExtensionAtoms] = [boxName: atomsData]
            }

            _ = CMVideoFormatDescriptionCreate(allocator: kCFAllocatorDefault, codecType: codecType.rawValue, width: codecpar.width, height: codecpar.height, extensions: extensions, formatDescriptionOut: &formatDescriptionOut)

            if let name = av_get_pix_fmt_name(format) {
                formatName = String(cString: name)
            } else {
                formatName = nil
            }
        } else if codecpar.codec_type == AVMEDIA_TYPE_SUBTITLE {
            mediaType = .subtitle
            audioDescriptor = nil
            formatName = nil
            bitDepth = 0
            let dic: NSMutableDictionary = [
                kCVImageBufferDisplayWidthKey: codecpar.width,
                kCVImageBufferDisplayHeightKey: codecpar.height,
            ]
            _ = CMFormatDescriptionCreate(allocator: kCFAllocatorDefault, mediaType: kCMMediaType_Subtitle, mediaSubType: codecType.rawValue, extensions: dic, formatDescriptionOut: &formatDescriptionOut)
        } else {
            bitDepth = 0
            return nil
        }
        formatDescription = formatDescriptionOut
        bitsPerRawSample = codecpar.bits_per_raw_sample
        isImageSubtitle = [AV_CODEC_ID_DVD_SUBTITLE, AV_CODEC_ID_DVB_SUBTITLE, AV_CODEC_ID_DVB_TELETEXT, AV_CODEC_ID_HDMV_PGS_SUBTITLE].contains(codecpar.codec_id)
        streamIndex = 0
    }

    /// 创建上下文
    func createContext(options: PlayerOptions) throws -> UnsafeMutablePointer<AVCodecContext> {
        try codecpar.createContext(options: options)
    }

    /// 是否启用
    public var isEnabled: Bool {
        get {
            stream?.pointee.discard == AVDISCARD_DEFAULT
        }
        set {
            var discard = newValue ? AVDISCARD_DEFAULT : AVDISCARD_ALL
            if mediaType == .subtitle, !isImageSubtitle {
                discard = AVDISCARD_DEFAULT
            }
            stream?.pointee.discard = discard
        }
    }
}

public extension FFmpegStreamAsset {
    var language: String? {
        languageCode.flatMap {
            Locale.current.localizedString(forLanguageCode: $0)
        }
    }

    var codecType: FourCharCode {
        mediaSubType.rawValue
    }

    /// 视频是否是高动态范围的
    var dynamicRange: DynamicRange? {
        if dolbyVisionConfig != nil { // 存在杜比视界信息
            return .dolbyVision
        } else {
            return formatDescription?.dynamicRange
        }
    }

    var colorSpace: CGColorSpace? {
        ColorUtils().GetColorSpace(ycbcrMatrix: yCbCrMatrix as CFString?, transferFunction: transferFunction as CFString?)
    }

    var mediaSubType: CMFormatDescription.MediaSubType {
        formatDescription?.mediaSubType ?? .boxed
    }

    var audioStreamBasicDescription: AudioStreamBasicDescription? {
        formatDescription?.audioStreamBasicDescription
    }

    var naturalSize: CGSize {
        formatDescription?.naturalSize ?? .zero
    }

    var colorPrimaries: String? {
        formatDescription?.colorPrimaries
    }

    var transferFunction: String? {
        formatDescription?.transferFunction
    }

    var yCbCrMatrix: String? {
        formatDescription?.yCbCrMatrix
    }
}

// MARK: - 创建 VT 硬解拓展参数 VTFormatExtensions

private func createVTFormatExtensions(format: AVPixelFormat, codecpar: AVCodecParameters) -> NSMutableDictionary {
    let extensions: NSMutableDictionary = [kCMFormatDescriptionExtension_FormatName: codecpar.codec_id.mediaSubType.rawValue.toString]

    // YCbCr without alpha uses 24. See
    // http://developer.apple.com/qa/qa2001/qa1183.html
    extensions[kCMFormatDescriptionExtension_Depth] = format.bitDepth * Int32(format.planeCount)

    // Set primaries.
    extensions[kCMFormatDescriptionExtension_ColorPrimaries] = ColorUtils().GetColorPrimaries(primaryId: codecpar.color_primaries) as String?

    // Set transfer function.
    extensions[kCMFormatDescriptionExtension_TransferFunction] = ColorUtils().GetTransferFunction(transferId: codecpar.color_trc) as String?

    // Set gamma level
    if codecpar.color_trc == AVCOL_TRC_GAMMA22 {
        extensions[kCMFormatDescriptionExtension_GammaLevel] = 2.2
    }
    if codecpar.color_trc == AVCOL_TRC_GAMMA28 {
        extensions[kCMFormatDescriptionExtension_GammaLevel] = 2.8
    }

    // Set matrix.
    extensions[kCMFormatDescriptionExtension_YCbCrMatrix] = ColorUtils().GetYCbCrMatrix(matrixId: codecpar.color_space) as String?

    // Set full range flag.
    let fullRange = codecpar.color_range == AVCOL_RANGE_JPEG
    extensions[kCMFormatDescriptionExtension_FullRangeVideo] = fullRange

    // https://chromium.googlesource.com/chromium/src/+/HEAD/media/gpu/mac/vt_config_util.mm
    // Set metadata for PQ signals.
    if codecpar.color_trc == AVCOL_TRC_SMPTE2084 {
//        print("PQ signal")
//        extensions[kCMFormatDescriptionExtension_MasteringDisplayColorVolume] = GetMasteringDisplayColorVolume(codecpar: codecpar)
//        extensions[kCMFormatDescriptionExtension_ContentLightLevelInfo] = GetContentLightLevelInfo(codecpar: codecpar)
    }

    // 硬件加速可能由于多种原因而不可用。一些常见情况是：
    // - 机器没有硬件加速功能
    // - 不支持请求的解码格式或配置
    // - 机器上的硬件解码资源正忙
    if #available(iOS 17.0, *) {
        extensions.setValue(true, forKey: kVTVideoDecoderSpecification_EnableHardwareAcceleratedVideoDecoder as String)
    }

    return extensions
}

// MARK: - 创建 VP9 解码 box

/// https://github.com/chromium/chromium/blob/main/media/gpu/mac/vt_config_util.mm#L159
private func createVP9CodecConfigurationBoxData(codecpar: AVCodecParameters) -> Data {
    // Synthesize a 'vpcC' box. See
    // https://www.webmproject.org/vp9/mp4/#vp-codec-configuration-box.
    let version: UInt8 = 1
    let profile: UInt8 = 0
    let level: UInt8 = 51
    let bitDepth: UInt8 = 8
    let chromaSubsampling: UInt8 = 1 // 4:2:0 colocated with luma (0, 0).
    var primaries: UInt8 = 1 // BT.709.
    var transfer: UInt8 = 1 // BT.709.
    var matrix: UInt8 = 1 // BT.709.

    primaries = UInt8(codecpar.color_primaries.rawValue)
    transfer = UInt8(codecpar.color_trc.rawValue)
    matrix = UInt8(codecpar.color_space.rawValue)

    var vpcc = [UInt8](repeating: 0, count: 12)
    vpcc[0] = version
    vpcc[4] = profile
    vpcc[5] = level
    vpcc[6] |= bitDepth << 4
    vpcc[6] |= chromaSubsampling << 1
    vpcc[7] = primaries
    vpcc[8] = transfer
    vpcc[9] = matrix

    return Data(vpcc)
}

func toCodecTagName(_ tag: UInt32) -> String {
    let tagStr = String(format: "%c%c%c%c",
                        tag & 0xFF,
                        (tag >> 8) & 0xFF,
                        (tag >> 16) & 0xFF,
                        (tag >> 24) & 0xFF)
    return tagStr
}
