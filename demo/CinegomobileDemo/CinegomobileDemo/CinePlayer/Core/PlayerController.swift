//
//  PlayerController.swift
//  PlayerController.swift
//  VideoPlayer
//
//  Created by Zero on 2024/10/12.
//

import AVFoundation
import AVKit
import SwiftUI
#if canImport(UIKit)
    import UIKit
#else
    import AppKit
#endif

public enum ItemLoadStatus: Int {
    case idle // 未开始
    case loading // 加载中
    case playable // 可播放
}

public enum MediaPlaybackStatus: Int {
    case idle // 未开始
    case playing // 播放中
    case paused // 暂停
    case seeking // 快进快退
    case finished // 播放完成
    case stopped // 停止
}

public enum CinePlayerStatus: CustomStringConvertible {
    case initialized // 初始化
    case preparing // 准备中
    case readyToPlay // 准备完成
    case buffering // 缓冲中
    case bufferFinished // 缓冲完成
    case paused // 暂停
    case playedToTheEnd // 播放完成
    case error // 错误

    /// 是否正在播放
    public var isPlaying: Bool { self == .buffering || self == .bufferFinished }

    public var description: String {
        switch self {
        case .initialized:
            return "initialized"
        case .preparing:
            return "preparing"
        case .readyToPlay:
            return "readyToPlay"
        case .buffering:
            return "buffering"
        case .bufferFinished:
            return "bufferFinished"
        case .paused:
            return "paused"
        case .playedToTheEnd:
            return "playedToTheEnd"
        case .error:
            return "error"
        }
    }
}

// MARK: - CinePlayerController

@MainActor
public class PlayerController: NSObject {
    /// 播放器配置，使用私有可以让当前播放配置上下文保持
    public private(set) var options: PlayerOptions

    /// 播放器资源
    private var playerItem: PlayerItem

    /// 通过这个修改播放器 UIView 的状态
    public weak var coordinatorDelegate: CoordinatorDelegate?

    /// 音频输出
    public let audioOutput: AudioOutput

    /// 视频显示图像区域
    public var videoView: VideoView? {
        didSet {
            oldValue?.invalidate()
            oldValue?.removeFromSuperview()
        }
    }

    /// 播放资源
    public private(set) var url: URL? = nil {
        didSet {
            if url == oldValue {
                play()
            } else {
                // 切换Url
                shutdown()
                replace(url: url, options: options)
                prepareToPlay()
            }
        }
    }

    /// 重设 URL
    public func setNewURL(url: URL? = nil, options: PlayerOptions) {
        self.options = options
        // url 有 didSet 方法处理 url 的变化
        self.url = url
    }

    /// 播放状态 设置一个初始化状态
    public var state = CinePlayerStatus.initialized {
        willSet {
            // 变更时
            if state != newValue {
                Task { @MainActor in
                    // 打印状态
                    CinePlayerLog(level: .debug, "State Change - \(newValue)")
                    // 回调给 UI
                    coordinatorDelegate?.stateChanged(controller: self, state: newValue)
                }
            }
        }
    }

    /// 播放状态(值)
    @MainActor
    public var playbackState = MediaPlaybackStatus.idle {
        didSet {
            if playbackState != oldValue {
                playOrPause()
                if playbackState == .finished {
                    finish(error: nil)
                }
            }
        }
    }

    // MARK: - 初始化

    init(url: URL?, options: PlayerOptions, coordinatorDelegate: CoordinatorDelegate? = nil) {
        // 初始化状态
        state = .initialized

        self.url = url
        self.options = options

        // 更新播放状态到 SwiftUI
        self.coordinatorDelegate = coordinatorDelegate

        // 新建播放器资源
        playerItem = PlayerItem(url: url, options: options)

        // 设置音频输出
        AudioUtils.setAudioSession()
        audioOutput = PlayerOptions.audioPlayerType.init()
        audioOutput.renderSource = playerItem

        // 设置视频输出
        videoView = VideoView(options: options)
        videoView?.renderSource = playerItem
        // 播放器画面填充模式
        videoView?.contentMode = .scaleAspectFit

        // 初始化变量，后面的函数中需要用到
        super.init()

        playerItem.controllerDelegate = self

        // 准备播放
        prepareToPlay()

        // 注册系统通知
//        registerForSystemNotifications()
    }

    // MARK: - 释放

    deinit {
        #if !os(macOS)
            try? AVAudioSession.sharedInstance().setPreferredOutputNumberOfChannels(2)
            try? AVAudioSession.sharedInstance().setPreferredOutputNumberOfChannels(2)
        #endif
        // 移除通知
        NotificationCenter.default.removeObserver(self)

        // TODO: 释放资源
//        videoView?.invalidate()
    }

    // MARK: - playerItem 相关

    ///  加载状态(值)
    @MainActor
    public var itemLoadState = ItemLoadStatus.idle {
        didSet {
            // 状态变化后，播放或暂停
            if itemLoadState != oldValue {
                playOrPause()
            }
        }
    }

    /// 资源是否准备好播放
    /// 这个数值是在 playerItem 中加载资源后调用 sourceDidOpened 进行更新
    public var isItemReadyToPlay = false

    /// playerItem 中调用 sourceDidOpened 进行的状态更新
    @MainActor
    public func readyToPlay() {
        state = .readyToPlay

        videoView?.subtitleModel.isHDR = videoDynamicRange?.isHDR ?? false

        DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 2) { [weak self] in
            guard let self else { return }
            if let subtitlesTrack = self.subtitlesTrack {
                loadSubtitle(subtitlesTrackIndex: subtitlesTrack.streamIndex)
            }
        }

        playNow()
    }

    // MARK: - 播放速率(值)

    public var playbackRate: Float = 1 {
        // 修改后设置倍速
        didSet {
            Task { @MainActor in
                if playbackRate != audioOutput.playbackRate {
                    audioOutput.playbackRate = playbackRate
                    playerItem.playbackRate = playbackRate

                    if audioOutput is AudioUnitPlayer {
                        var audioFilters = options.audioFilters.filter {
                            !$0.hasPrefix("atempo=")
                        }
                        if playbackRate != 1 {
                            audioFilters.append("atempo=\(playbackRate)")
                        }
                        options.audioFilters = audioFilters
                    }
                }
            }
        }
    }

    // MARK: - 章节列表(值)

    var chapters: [Chapter] {
        playerItem.chapters
    }

    // MARK: - 音量(值)

    /// 通过修改这个值来修改音量
    var playbackVolume: Float {
        get {
            audioOutput.volume
        }
        set {
            audioOutput.volume = newValue
        }
    }

    // MARK: - 当前播放时间(值)

    var currentPlaybackTime: TimeInterval {
        playerItem.currentPlaybackTime
    }

    // MARK: - 总时长(值)

    var duration: TimeInterval {
        playerItem.duration
    }

    // MARK: - 文件大小(值)

    var fileSize: Double {
        playerItem.fileSize
    }

    // MARK: - 可快进快退(值)

    var seekable: Bool {
        playerItem.seekable
    }

    // MARK: - 播放时的动态数据(值)

    var dynamicPlayerInfo: PlayerDynamicInfo? {
        playerItem.dynamicInfo
    }

    // MARK: - 视图显示模式(值)

    var viewContentMode: UIViewContentMode {
        get {
            videoView?.contentMode ?? .center
        }
        set {
            videoView?.contentMode = newValue
        }
    }

    // MARK: - 静音状态(值)

    var isMuted: Bool {
        get {
            audioOutput.isMuted
        }
        set {
            audioOutput.isMuted = newValue
        }
    }

    // MARK: - 设置新的媒体资源(方法)

    @MainActor
    public func replace(url: URL? = nil, options: PlayerOptions) {
        resetMedia(item: PlayerItem(url: url!, options: options))
    }

    @MainActor
    func resetMedia(item: PlayerItem) {
        CinePlayerLog(level: .debug, "reset media \(self)")

        shutdown()
        playerItem.controllerDelegate = nil

        options = item.options
        playerItem = item

        playerItem.controllerDelegate = self
        audioOutput.flush()
        audioOutput.renderSource = playerItem
        videoView?.renderSource = playerItem
        videoView?.options = options
    }

    // MARK: - 播放或暂停(方法)

    /// 判断当前播放状态，如果是播放状态，暂停，否则播放
    func playOrPause() {
//        Task { @MainActor in
        let isPaused = !(playbackState == .playing && itemLoadState == .playable)
        if isPaused {
            audioOutput.pause()
            videoView?.pause()
        } else {
            audioOutput.play()
            videoView?.play()
        }
        changeLoadState()
//        }
    }

    // MARK: - 快进快退(方法)

    /// 快进快退、跳转到某个时间点
    @MainActor
    public func seek(time: TimeInterval, completion: @escaping ((Bool) -> Void)) {
        // 时间无效
        if time.isInfinite || time.isNaN {
            completion(false)
        }

        if isItemReadyToPlay, seekable {
            seek2(time: time) { [weak self] finished in
                guard let self else { return }

                completion(finished)

                if finished {
                    state = .buffering
                    self.playNow()
                }
            }
        } else {
            completion(false)
        }
    }

    func seek2(time: TimeInterval, completion: @escaping ((Bool) -> Void)) {
        let time = max(time, 0)
        playbackState = .seeking

        playerItem.seek(time: time) { [weak self] result in
            guard let self else { return }
            if result {
                self.audioOutput.flush()
            }
            completion(result)
        }
    }

    // MARK: - 准备播放(方法)

    /// 准备播放，修改状态
    func prepareToPlay() {
        // 准备播放状态
        state = .preparing
        playerItem.prepareToPlay()
    }

    // MARK: - 停止(方法)

    @MainActor
    public func shutdown() {
        playbackState = .stopped
        itemLoadState = .idle
        isItemReadyToPlay = false

        timer.invalidate()
        playerItem.shutdown()

        audioOutput.pause()
        audioOutput.flush()
        videoView?.flush()
//        UIApplication.shared.isIdleTimerDisabled = false
    }

    // MARK: - 媒体轨道列表(方法)

    func tracks(mediaType: AVFoundation.AVMediaType) -> [FFmpegStreamAsset] {
        playerItem.allAssetList.compactMap { track -> FFmpegStreamAsset? in
            if track.mediaType == mediaType {
                return track
            }
            return nil
        }
    }

    // MARK: - 选择轨道(方法)

    func select(track: some FFmpegStreamAsset) {
        let isSeek = playerItem.select(track: track)
        if isSeek {
            audioOutput.flush()
        }
    }

    // MARK: 定时器

    /// 设置 0.05s 的监听，对齐字幕、更新进度条
    lazy var timer: Timer = .scheduledTimer(withTimeInterval: 0.05, repeats: true) { [weak self] _ in
        guard let self else {
            return
        }

        Task { @MainActor in
            if !self.isItemReadyToPlay {
                return
            }

            // 对齐字幕时间
            // TODO: 这里可以增加字幕延时的功能
            let size = self.tracks(mediaType: .video).first { $0.isEnabled }?.naturalSize ?? CGSize(width: 1920, height: 1080)
            self.videoView?.subtitleModel.subtitle(currentTime: self.currentPlaybackTime, size: size)

            // 更新 UIView 中的进度条
            self.coordinatorDelegate?.timeChanged(controller: self, currentTime: self.currentPlaybackTime, totalTime: self.duration)

            // 防止一直缓冲
            if self.playbackState == .playing, self.itemLoadState == .playable, self.state == .buffering {
                // 一个兜底保护，正常不能走到这里
                self.state = .bufferFinished
            }
        }
    }

    // MARK: - 播放(方法)

    @MainActor
    func playNow() {
//        UIApplication.shared.isIdleTimerDisabled = true

        if state == .error || state == .initialized {
            prepareToPlay()
        }

        CinePlayerLog(level: .info, "Play state: \(state)")
        CinePlayerLog(level: .info, "PlayerController loadState: \(itemLoadState)")

        switch itemLoadState {
        case .loading:
            state = .buffering
        case .playable:
            state = .bufferFinished
        default:
            break
        }

        if isItemReadyToPlay {
            if state == .playedToTheEnd {
                // 已经播放到文件最后了，回到开头
                seek2(time: 0) { [weak self] finished in
                    guard let self else { return }
                    if finished {
                        self.playbackState = .playing
                    }
                }
            } else {
                playbackState = .playing
            }
            // 立即触发定时器内
            timer.fireDate = Date.distantPast
        }
    }

    /// 返回视频的动态范围
    var videoDynamicRange: DynamicRange? {
        tracks(mediaType: .video).first { $0.isEnabled }?.dynamicRange
    }

    /// 更改加载状态
    @MainActor
    public func changeLoadState() {
        if playbackState != .seeking { return }

        guard state.isPlaying else { return }

        if itemLoadState == .playable {
            state = .bufferFinished
        } else {
            state = .buffering
        }
    }

    /// 结束播放
    @MainActor
    public func finish(error: Error?) {
        // 暂停定时器
        timer.fireDate = Date.distantFuture

        // 恢复屏幕常亮
//        UIApplication.shared.isIdleTimerDisabled = false

        if let error {
            state = .error
            Task { @MainActor in
                coordinatorDelegate?.finish(controller: self, finish: error)
                CinePlayerLog(level: .error, error as CustomStringConvertible)
            }
        } else {
            state = .playedToTheEnd
        }

        // 修改 UI 上的播放时间进度
        Task { @MainActor in
            coordinatorDelegate?.timeChanged(controller: self, currentTime: duration, totalTime: duration)
        }
    }

    var currentSubtitleStreamIndex: Int32 {
        playerItem.currentSubtitleStreamIndex
    }

    var subtitleTracks: [FFmpegStreamAsset] {
        tracks(mediaType: .subtitle)
    }
}

// MARK: - 字幕相关

extension PlayerController {
    var subtitlesTrack: FFmpegStreamAsset? {
        tracks(mediaType: .subtitle).first { $0.isEnabled } ?? nil
    }

    // TODO: 使用了 ass 生成图片，会有很多内存占用
    // 需要在切换字幕的时候清理 libass 的 ass img 渲染器
    // 有些内嵌字幕还需要处理，比如金刚的字幕除了英文别的都不行
    func loadSubtitle(subtitlesTrackIndex: Int32) {
        if let track = subtitleTracks.first(where: { $0.streamIndex == subtitlesTrackIndex }) {
            playerItem.currentSubtitleStreamIndex = track.streamIndex

            // select 必须放在 设置 StreamIndex 后面
            select(track: track)

            let embedSubtitle = EmbedSubtitleDataSouce(playItem: playerItem, subtitlesTrack: track)

            videoView?.subtitleModel.setSubtitle(info: embedSubtitle)
        }
    }
}

extension PlayerController {
    /// 注册系统通知回调
    func registerForSystemNotifications() {
        #if canImport(UIKit)
//            Task { @MainActor in
            NotificationCenter.default.addObserver(self, selector: #selector(enterBackground), name: UIApplication.didEnterBackgroundNotification, object: nil)
            NotificationCenter.default.addObserver(self, selector: #selector(enterForeground), name: UIApplication.willEnterForegroundNotification, object: nil)
//            }
        #endif

        // 空间音频状态变化通知
        #if !os(macOS)
            if #available(tvOS 15.0, iOS 15.0, *) {
                NotificationCenter.default.addObserver(self, selector: #selector(spatialCapabilityChange), name: AVAudioSession.spatialPlaybackCapabilitiesChangedNotification, object: nil)
            }
            if #available(tvOS 15.0, iOS 15.0, *) {
                NotificationCenter.default.addObserver(self, selector: #selector(spatialCapabilityChange), name: AVAudioSession.spatialPlaybackCapabilitiesChangedNotification, object: nil)
            }
        #endif

        #if !os(macOS)
            // 监听音频中断通知，一般发生在切换到别的媒体播放 app
//            Task { @MainActor in
            NotificationCenter.default.addObserver(self, selector: #selector(audioInterrupted), name: AVAudioSession.interruptionNotification, object: nil)
//            }
        #endif
    }

    // MARK: -  空间音频功能变化

    @objc private func spatialCapabilityChange(notification _: Notification) {
        CinePlayerLog(level: .debug, "[audio] spatialCapabilityChange")
        for track in tracks(mediaType: .audio) {
            track.audioDescriptor?.updateAudioFormat()
        }
    }

    /// init 时注册的通知处理
    /// 进入后台
    @MainActor
    @objc private func enterBackground() {
        guard state.isPlaying else {
            return
        }
        // TODO: 保存最后播放的时间和图片

        pause()
    }

    /// 进入前台
    @MainActor
    @objc private func enterForeground() {}

    #if !os(macOS)
        // 当音频被中断时，暂停

        @objc private func audioInterrupted(notification: Notification) {
            guard let userInfo = notification.userInfo,
                  let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
                  let type = AVAudioSession.InterruptionType(rawValue: typeValue)
            else {
                return
            }
            switch type {
            case .began:
                pause()

            default:
                break
            }
        }
    #endif
}

// MARK: - 基本控制方法 用于 UI 中调用

public extension PlayerController {
    // 开始播放

    @MainActor
    func play() {
        playbackState = .playing
    }

    /// 暂停播放
    @MainActor
    func pause() {
        playbackState = .paused

        // 暂停定时器
        timer.fireDate = Date.distantFuture
        state = .paused

        UIApplication.shared.isIdleTimerDisabled = false
    }

    /// 快进快退固定时间
    @MainActor
    func skip(interval: Int) {
        seek(time: currentPlaybackTime + TimeInterval(interval))
    }

    /// 定位到某个时间点
    @MainActor
    func seek(time: TimeInterval) {
        seek(time: TimeInterval(time)) { _ in }
    }
}
