//
//  CoordinatorDelegate.swift
//  VideoPlayer
//
//  Created by Zero on 2024/10/13.
//

import Foundation
#if canImport(UIKit)
    import UIKit
#endif

@MainActor
public protocol CoordinatorDelegate: AnyObject {
    /// 状态更新
    func stateChanged(controller: PlayerController, state: CinePlayerStatus)

    /// 时间更新
    func timeChanged(controller: PlayerController, currentTime: TimeInterval, totalTime: TimeInterval)

    /// 完成播放
    func finish(controller: PlayerController, finish error: Error?)
}

/// 在 controller  访问 CinePlayer.Coordinator 中的属性
extension CinePlayer.Coordinator: CoordinatorDelegate {
    public func stateChanged(controller: PlayerController, state: CinePlayerStatus) {
        // 触发 SwiftUI 上的调用的 callBack
        onStateChanged?(controller, state)

        if state == .readyToPlay {
            playbackRate = controller.playbackRate
        } else if state == .bufferFinished {
//            isMaskShow = false
        } else {
            // TODO: 手势操作 应该调整到最外层，而不是播放器视图层
            #if canImport(UIKit)
                if state == .preparing, let view = controller.videoView {
                    // 向左
                    let swipeLeft = UISwipeGestureRecognizer(target: self, action: #selector(swipeGestureAction(_:)))
                    swipeLeft.direction = .left
                    view.addGestureRecognizer(swipeLeft)
                    // 向右
                    let swipeRight = UISwipeGestureRecognizer(target: self, action: #selector(swipeGestureAction(_:)))
                    swipeRight.direction = .right
                    view.addGestureRecognizer(swipeRight)
                    // 向上
                    let swipeUp = UISwipeGestureRecognizer(target: self, action: #selector(swipeGestureAction(_:)))
                    swipeUp.direction = .up
                    view.addGestureRecognizer(swipeUp)
                    // 向下
                    let swipeDown = UISwipeGestureRecognizer(target: self, action: #selector(swipeGestureAction(_:)))
                    swipeDown.direction = .down
                    view.addGestureRecognizer(swipeDown)
                }
            #endif
        }
    }

    /// 播放时间改变，更新进度条
    public func timeChanged(controller _: PlayerController, currentTime: TimeInterval, totalTime: TimeInterval) {
        guard var current = Int(exactly: ceil(currentTime)), var total = Int(exactly: ceil(totalTime)) else {
            return
        }
        current = max(0, current)
        total = max(0, total)
        if progress.currentTime != current {
            progress.currentTime = current
        }
        if total == 0 {
            progress.totalTime = progress.currentTime
        } else {
            if progress.totalTime != total {
                progress.totalTime = total
            }
        }
    }

    public func finish(controller: PlayerController, finish error: Error?) {
        onFinish?(controller, error)
    }
}
