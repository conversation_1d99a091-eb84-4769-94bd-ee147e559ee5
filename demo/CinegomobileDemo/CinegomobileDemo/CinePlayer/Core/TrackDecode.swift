import AVFoundation
import CFFmpeg
import CoreMedia

/// 定义一个协议，媒体轨道的解码
/// 具有缓冲协议用于添加到缓冲列表
protocol TrackDecodeProtocol: BufferProtocol, AnyObject {
    /// 初始化方法，接受媒体类型、帧队列长度和播放器选项作为参数
    init(mediaType: AVFoundation.AVMediaType, frameQueueLength: UInt8, options: PlayerOptions)

    /// 表示文件是否结束的布尔值
    var isEndOfFile: Bool { get set }

    /// playerItem委托
    var playerItemDelegate: PlayerItemDelegate? { get set }

    /// 解码方法
    func decode()

    /// 跳转到指定时间
    func seek(time: TimeInterval)

    /// 放入数据包
    func putPacket(packet: Packet)

    /// 关闭方法
    func shutdown()
}

/// 最终类不可以被继承，优化编译器性能
final class TrackDecode<Frame: CineFrame>: TrackDecodeProtocol, CustomStringConvertible {
    /// 跳转时间
    var seekTime = 0.0

    /// 播放器选项
    fileprivate let options: PlayerOptions

    /// 解码器映射
    fileprivate var decoderMap = [Int32: DecodeProtocol]()

    /// 播放器状态
    fileprivate var state = CineCodecState.idle {
        didSet {
            if state == .finished {
                seekTime = 0
            }
        }
    }

    /// 文件是否结束的标志
    var isEndOfFile: Bool = false

    /// 数据包队列
    var packetQueue = FrameQueue<Packet>()

    /// 数据包数量
    var packetCount: Int { packetQueue.count }

    /// 实现 CustomStringConvertible 协议，提供描述属性
    let description: String

    /// playerItem委托
    weak var playerItemDelegate: PlayerItemDelegate?

    /// 媒体类型
    let mediaType: AVFoundation.AVMediaType

    /// 输出渲染队列
    let outputRenderQueue: FrameQueue<Frame>

    /// 帧数量
    var frameCount: Int { outputRenderQueue.count }

    /// 最大帧数量
    var frameMaxCount: Int {
        outputRenderQueue.maxCount
    }

    /// 帧率
    var fps: Float {
        outputRenderQueue.fps
    }

    /// 初始化方法
    required init(mediaType: AVFoundation.AVMediaType, frameQueueLength: UInt8, options: PlayerOptions) {
        self.options = options
        self.mediaType = mediaType

        description = mediaType.rawValue

        // 根据媒体类型初始化输出渲染队列
        if mediaType == .audio {
            outputRenderQueue = FrameQueue(length: Int(frameQueueLength), expanding: false)
        } else if mediaType == .video {
            outputRenderQueue = FrameQueue(length: Int(frameQueueLength), sorted: true, expanding: false)
        } else {
            outputRenderQueue = FrameQueue(length: Int(frameQueueLength), sorted: true)
        }

        // 初始化操作队列
        operationQueue.name = "CinePlayer_" + mediaType.rawValue
        operationQueue.maxConcurrentOperationCount = 1
        operationQueue.qualityOfService = .userInteractive
    }

    // MARK: 获取输出渲染

    @MainActor
    func getOutputRender(where predicate: ((Frame, Int) -> Bool)?) -> Frame? {
        let outputFecthRender = outputRenderQueue.dequeue(where: predicate)
        if outputFecthRender == nil {
            // 渲染帧队列已经空了
            if state == .finished, frameCount == 0 {
                // 通知解码
                playerItemDelegate?.codecDidFinished(track: self)
            }
        }
        return outputFecthRender
    }

    // MARK: 创建解码器

    func makeDecode(assetTrack: FFmpegStreamAsset) -> DecodeProtocol {
        autoreleasepool {
            if mediaType == .subtitle {
                return SubtitleDecode(assetTrack: assetTrack, options: options)
            }
            if assetTrack.mediaType == .video, options.videoToolBoxDecode,
               let session = DecompressionSession(assetTrack: assetTrack, options: options)
            {
                return VideoToolboxDecode(options: options, session: session)
            } else {
                return FFmpegDecode(assetTrack: assetTrack, options: options)
            }
        }
    }

    /// 上一个数据包的字节数
    private var lastPacketBytes = Int32(0)

    /// 上一个数据包的秒数
    private var lastPacketSeconds = Double(-1)

    /// 比特率
    var bitrate = Double(0)

    // MARK: 解码数据包

    fileprivate func doDecode(packet: Packet) {
        guard packet.corePacket != nil else {
            return
        }

        // 处理关键帧
        if packet.isKeyFrame, packet.assetTrack.mediaType != .subtitle {
            let seconds = packet.seconds
            let diff = seconds - lastPacketSeconds
            if lastPacketSeconds < 0 || diff < 0 {
                bitrate = 0
                lastPacketBytes = 0
                lastPacketSeconds = seconds
            } else if diff > 1 {
                bitrate = Double(lastPacketBytes) / diff
                lastPacketBytes = 0
                lastPacketSeconds = seconds
            }
        }

        lastPacketBytes += packet.size

        // 从解码器映射中取出解码器
        let decoder = decoderMap.value(for: packet.assetTrack.streamIndex, default: makeDecode(assetTrack: packet.assetTrack))

        // 解码帧
        decoder.decodeFrame(from: packet) { [weak self, weak decoder] result in
            guard let self else {
                return
            }
            do {
                let frame = try result.get()

                if self.state == .flush || self.state == .closed {
                    return
                }
                if self.seekTime > 0 {
                    let timestamp = frame.timestamp + frame.duration
                    if timestamp <= 0 || frame.timebase.cmtime(for: timestamp).seconds < self.seekTime {
                        return
                    } else {
                        self.seekTime = 0.0
                    }
                }

                if let frame = frame as? Frame {
                    // 成功解码的帧放入输出队列
                    self.outputRenderQueue.enqueue(frame)
                    self.outputRenderQueue.fps = packet.assetTrack.nominalFrameRate
                }
            } catch {
                // 失败后切换解码器

                DispatchQueue.global().async {
                    // 关闭当前解码器
                    decoder?.shutdown()
                }

                // 解码失败切换解码器
                CinePlayerLog(level: .error, "解码失败: \(error)")

                // 使用原生 VideoToolbox 失败，切换到 FFmpeg VideoToolbox
                if decoder is VideoToolboxDecode {
                    CinePlayerLog(level: .error, "使用原生 VideoToolbox 失败，切换到 FFmpeg VideoToolbox")

                    // 关闭原生硬解码
                    self.options.videoToolBoxDecode = false

                    // 修改解码器映射中的解码器
                    self.decoderMap[packet.assetTrack.streamIndex] = FFmpegDecode(assetTrack: packet.assetTrack, options: self.options)

                    // 当前帧还是要解码的
                    self.doDecode(packet: packet)

                    // 使用 FFmpeg VideoToolbox 失败，切换到 FFmpeg 软解
                } else if decoder is FFmpegDecode && self.options.ffmpegVTDecode {
                    CinePlayerLog(level: .error, "使用 FFmpeg VideoToolbox 失败，切换到 FFmpeg 软解")

                    // 关闭 FFmpeg 硬解码
                    self.options.ffmpegVTDecode = false

                    // 修改解码器映射中的解码器
                    self.decoderMap[packet.assetTrack.streamIndex] = FFmpegDecode(assetTrack: packet.assetTrack, options: self.options)

                    // 当前帧还是要解码的
                    self.doDecode(packet: packet)

                } else {
                    CinePlayerLog(level: .error, "所有的解码器解码失败，播放失败")
                    self.state = .failed
                }
            }
        }
    }

    /// 操作队列
    private let operationQueue = OperationQueue()

    /// 解码操作
    private var decodeOperation: BlockOperation!

    /// 放入数据包
    func putPacket(packet: Packet) {
        packetQueue.enqueue(packet)
    }

    /// 解码方法
    func decode() {
        isEndOfFile = false
        guard operationQueue.operationCount == 0 else { return }
        decodeOperation = BlockOperation { [weak self] in
            guard let self else { return }
            Thread.current.name = self.operationQueue.name
            Thread.current.stackSize = 65536

            self.decodeThread()
        }
        decodeOperation.queuePriority = .veryHigh
        decodeOperation.qualityOfService = .userInteractive
        operationQueue.addOperation(decodeOperation)
    }

    /// 解码线程
    private func decodeThread() {
        state = .decoding
        isEndOfFile = false
        decoderMap.values.forEach { $0.doFlushCodec() }
        outerLoop: while !decodeOperation.isCancelled {
            switch state {
            case .idle:
                break outerLoop

            case .finished, .closed, .failed:
                decoderMap.values.forEach { $0.shutdown() }
                decoderMap.removeAll()
                break outerLoop

            case .flush:
                decoderMap.values.forEach { $0.doFlushCodec() }
                state = .decoding

            case .decoding:
                if isEndOfFile, packetQueue.count == 0 {
                    state = .finished
                } else {
                    guard let packet = packetQueue.dequeue(wait: true), state != .flush, state != .closed else {
                        continue
                    }
                    autoreleasepool {
                        doDecode(packet: packet)
                    }
                }
            }
        }
    }

    // MARK: SEEK

    func seek(time _: TimeInterval) {
        if decodeOperation.isFinished {
            decode()
        }
        packetQueue.flush()

        // 设置跳转时间
        seekTime = 0

        isEndOfFile = false
        state = .flush
        outputRenderQueue.flush()
    }

    /// 关闭方法
    func shutdown() {
        if state == .idle {
            return
        }
        decoderMap.values.forEach { $0.shutdown() }
        state = .closed
        outputRenderQueue.shutdown()
        packetQueue.shutdown()
    }
}

/// 扩展 Dictionary，提供一个方法来获取或设置默认值
public extension Dictionary {
    mutating func value(for key: Key, default defaultValue: @autoclosure () -> Value) -> Value {
        if let value = self[key] {
            return value
        } else {
            let value = defaultValue()
            self[key] = value
            return value
        }
    }
}
