//
//  PlayerControllerDelegate.swift
//  VideoPlayer
//
//  Created by Zero on 2024/10/13.
//

import AVFoundation

// 在 playerItem 中加载资源后调用函数进行 controller 的状态更新

/// playerItem 返回的状态
public struct ItemLoadingState {
    public let loadedTime: TimeInterval // 已加载时间（可用于播放的时长）
    public let progress: TimeInterval // 进度
    public let packetCount: Int // 包计数
    public let frameCount: Int // 帧计数
    public let isEndOfFile: Bool // 是否文件结束
    public let isPlayable: Bool // 是否可播放
    public let isSeek: Bool // 是否在查找
}

@MainActor
protocol PlayerControllerDelegate: AnyObject {
    func sourceDidOpened()
    func sourceDidChange(itemloadingState: ItemLoadingState)
    func sourceDidFailed(error: NSError?)
    func sourceDidFinished()
}

extension PlayerController: PlayerControllerDelegate {
    /// 打开媒体资源 只会执行一次
    func sourceDidOpened() {
        isItemReadyToPlay = true

        // 视频
        let vidoeTracks = tracks(mediaType: .video)
        if vidoeTracks.isEmpty {
            videoView = nil
        }
        // 音频
        let audioDescriptor = tracks(mediaType: .audio).first { $0.isEnabled }.flatMap {
            $0
        }?.audioDescriptor

        Task { @MainActor in
            if let audioDescriptor {
                audioOutput.prepare(audioFormat: audioDescriptor.audioFormat)
            }

            readyToPlay()
        }
    }

    /// 资源状态更新
    func sourceDidChange(itemloadingState: ItemLoadingState) {
        if itemLoadState == .playable {
            if !itemloadingState.isEndOfFile &&
                itemloadingState.frameCount == 0 &&
                itemloadingState.packetCount == 0 &&
                options.preferredForwardBufferDuration != 0
            {
                itemLoadState = .loading
            }
        } else {
            if itemloadingState.isPlayable {
                itemLoadState = .playable
            }
        }
    }

    /// 解析资源失败了
    func sourceDidFailed(error: NSError?) {
        finish(error: error)
    }

    func sourceDidFinished() {
        playbackState = .finished
    }
}
