import AVFoundation
import Foundation

// 定义一个类 AudioRendererPlayer，实现 AudioOutput 协议
public class AudioRendererPlayer: AudioOutput {
    // 播放速率 倍速
    public var playbackRate: Float = 1 {
        didSet {
            if !isPaused {
                synchronizer.rate = playbackRate
            }
        }
    }

    // 音量
    public var volume: Float {
        get {
            renderer.volume
        }
        set {
            renderer.volume = newValue
        }
    }

    // 是否静音
    public var isMuted: Bool {
        get {
            renderer.isMuted
        }
        set {
            renderer.isMuted = newValue
        }
    }

    public weak var renderSource: OutputRenderSourceDelegate?
    private var periodicTimeObserver: Any? // 定期时间观察者
    private let renderer = AVSampleBufferAudioRenderer() // 音频渲染器
    private let synchronizer = AVSampleBufferRenderSynchronizer() // 渲染同步器
    private let serializationQueue = DispatchQueue(label: "cine.player.audio.render.queue") // 序列化队列
    var isPaused: Bool {
        synchronizer.rate == 0
    }

    // 初始化方法
    public required init() {
        synchronizer.addRenderer(renderer)
        if #available(macOS 11.3, iOS 14.5, tvOS 14.5, *) {
            synchronizer.delaysRateChangeUntilHasSufficientMediaData = false
        }
    }

    // 准备音频格式
    public func prepare(audioFormat: AVAudioFormat) {
        #if !os(macOS)
            try? AVAudioSession.sharedInstance().setPreferredOutputNumberOfChannels(Int(audioFormat.channelCount))
            CinePlayerLog(level: .debug, "[audio] set preferredOutputNumberOfChannels: \(audioFormat.channelCount)")
        #endif
    }

    // 播放音频
    @MainActor
    public func play() {
        let time: CMTime
        if #available(macOS 11.3, iOS 14.5, tvOS 14.5, *) {
            // 判断是否有足够的缓存，有的话就用当前的时间。seek的话，需要清空缓存，这样才能取到最新的时间。
            if renderer.hasSufficientMediaDataForReliablePlaybackStart {
                time = synchronizer.currentTime()
            } else {
                if let currentRender = renderSource?.getAudioOutputRender() {
                    time = currentRender.cmtime
                } else {
                    time = .zero
                }
            }
        } else {
            if let currentRender = renderSource?.getAudioOutputRender() {
                time = currentRender.cmtime
            } else {
                time = .zero
            }
        }
        synchronizer.setRate(playbackRate, time: time)
        // 要手动的调用下，这样才能及时的更新音频的时间
        renderSource?.setAudio(time: time, position: -1)
        renderer.requestMediaDataWhenReady(on: serializationQueue) { [weak self] in
            guard let self else {
                return
            }
            self.request()
        }
        periodicTimeObserver = synchronizer.addPeriodicTimeObserver(forInterval: CMTime(seconds: 0.01), queue: .main) { [weak self] time in
            guard let self else {
                return
            }
            self.renderSource?.setAudio(time: time, position: -1)
        }
    }

    // 暂停音频
    public func pause() {
        synchronizer.rate = 0
        renderer.stopRequestingMediaData()
        if let periodicTimeObserver {
            synchronizer.removeTimeObserver(periodicTimeObserver)
            self.periodicTimeObserver = nil
        }
    }

    // 刷新音频
    public func flush() {
        renderer.flush()
    }

    // 请求音频数据
    @MainActor
    private func request() {
        while renderer.isReadyForMoreMediaData, !isPaused {
            guard var render = renderSource?.getAudioOutputRender() else {
                break
            }
            var array = [render]
            let loopCount = Int32(render.audioFormat.sampleRate) / 20 / Int32(render.numberOfSamples) - 2
            if loopCount > 0 {
                for _ in 0 ..< loopCount {
                    if let render = renderSource?.getAudioOutputRender() {
                        array.append(render)
                    }
                }
            }
            if array.count > 1 {
                render = AudioFrame(array: array)
            }
            if let sampleBuffer = render.toCMSampleBuffer() {
                let channelCount = render.audioFormat.channelCount
                renderer.audioTimePitchAlgorithm = channelCount > 2 ? .spectral : .timeDomain
                renderer.enqueue(sampleBuffer)
                #if !os(macOS)
                    if AVAudioSession.sharedInstance().preferredInputNumberOfChannels != channelCount {
                        try? AVAudioSession.sharedInstance().setPreferredOutputNumberOfChannels(Int(channelCount))
                    }
                #endif
            }
        }
    }
}
