//
//  Audio.swift
//  VideoPlayer
//
//  Created by Zero on 2024/10/4.
//

import AVFoundation
import Foundation

public class AudioUtils {
    /// 设置音频会话
    static func setAudioSession() {
        #if os(macOS)
        // try? AVAudioSession.sharedInstance().setRouteSharingPolicy(.longFormAudio)
        #else
            var category = AVAudioSession.sharedInstance().category
            if category != .playAndRecord {
                category = .playback
            }
            #if os(tvOS)
                try? AVAudioSession.sharedInstance().setCategory(category, mode: .moviePlayback, policy: .longFormAudio)
            #else
                try? AVAudioSession.sharedInstance().setCategory(category, mode: .moviePlayback, policy: .longFormVideo)
            #endif
            try? AVAudioSession.sharedInstance().setActive(true) // 激活音频会话
        #endif
    }

    #if !os(macOS)
        /// 检查空间音频是否启用
        static func isSpatialAudioEnabled(channelCount _: AVAudioChannelCount) -> Bool {
            if #available(tvOS 15.0, iOS 15.0, *) {
                let isSpatialAudioEnabled = AVAudioSession.sharedInstance().currentRoute.outputs.contains { $0.isSpatialAudioEnabled }
                try? AVAudioSession.sharedInstance().setSupportsMultichannelContent(isSpatialAudioEnabled)
                return isSpatialAudioEnabled
            } else {
                return false
            }
        }

        /// 音频输出声道数量
        static func outputNumberOfChannels(channelCount: AVAudioChannelCount) -> AVAudioChannelCount {
            let maximumOutputNumberOfChannels = AVAudioChannelCount(AVAudioSession.sharedInstance().maximumOutputNumberOfChannels)
            let preferredOutputNumberOfChannels = AVAudioChannelCount(AVAudioSession.sharedInstance().preferredOutputNumberOfChannels)
            let isSpatialAudioEnabled = isSpatialAudioEnabled(channelCount: channelCount)
            CinePlayerLog(level: .debug, "[audio] maximumOutputNumberOfChannels: \(maximumOutputNumberOfChannels), preferredOutputNumberOfChannels: \(preferredOutputNumberOfChannels), isSpatialAudioEnabled: \(isSpatialAudioEnabled)")
            let maxRouteChannelsCount = AVAudioSession.sharedInstance().currentRoute.outputs.compactMap {
                $0.channels?.count
            }.max() ?? 2
            CinePlayerLog(level: .debug, "[audio] currentRoute max channels: \(maxRouteChannelsCount)")
            var channelCount = channelCount
            if channelCount > 2 {
                let minChannels = min(maximumOutputNumberOfChannels, channelCount)
                #if os(tvOS) || targetEnvironment(simulator)
                    if !isSpatialAudioEnabled {
                        // 不要用maxRouteChannelsCount来判断，有可能会不准。导致多音道设备也返回2（一开始播放一个2声道，就容易出现），也不能用outputNumberOfChannels来判断，有可能会返回2
                        // channelCount = AVAudioChannelCount(min(AVAudioSession.sharedInstance().outputNumberOfChannels, maxRouteChannelsCount))
                        channelCount = minChannels
                    }
                #else
                    // iOS 外放是会自动有空间音频功能，但是蓝牙耳机有可能没有空间音频功能或者把空间音频给关了，。所以还是需要处理。
                    if !isSpatialAudioEnabled {
                        channelCount = minChannels
                    }
                #endif
            } else {
                channelCount = 2
            }
            // 不在这里设置setPreferredOutputNumberOfChannels,因为这个方法会在获取音轨信息的时候，进行调用。
            CinePlayerLog(level: .debug, "[audio] outputNumberOfChannels: \(AVAudioSession.sharedInstance().outputNumberOfChannels) output channelCount: \(channelCount)")
            return channelCount
        }
    #endif
}
