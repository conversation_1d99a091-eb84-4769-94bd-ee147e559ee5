import AVFoundation
import SwiftUI

#if canImport(UIKit)
    import UIKit
#else
    import AppKit
#endif

/// PlayerOptions类用于配置视频播放的选项
public class PlayerOptions {
    /// 音频播放器类型
    /// AudioEnginePlayer 在 IOS 下音轨有问题，人声不输出
    /// AudioUnitPlayer切换音轨有问题，不过后续可以尝试空间音频混音
    /// AudioRendererPlayer
    static var audioPlayerType: AudioOutput.Type = AudioRendererPlayer.self

    /// 最低缓存视频时间 缓存多长可以开始播放
    public let preferredForwardBufferDuration = 0.5

    /// 最大缓存视频时间
    public let maxBufferDuration = 5.0

    /// 播放起始时间 用于继续播放
    public let startPlayTime: TimeInterval = 0

    /// 音频相关
    public var audioFilters = [String]() // 音频过滤器 用于倍速播放等

    // 视频相关
    public var videoFilters = [String]() // 视频过滤器 用于视频旋转 去交错等
    public var videoDelay = 0.0 // 视频延迟时间（秒） 音视频同步基于音频，可以使用这个变相达到调整音频延迟

    /// videoToolBoxDecode和ffmpegVTDecode都为false时，采用ffmpeg软解码
    /// 视频优先使用videoToolBox硬件解码，解码器不支持时自动切换ffmpeg解码
    public var videoToolBoxDecode = false

    /// ffmpeg使用videoToolBox硬件解码 (在videoToolBoxDecode=false时有效)
    public var ffmpegVTDecode = true

    /// 视频去交错 这个给用户自己选择开启比较好
    public var videoDeInterlace = false

    /// 设置目标动态范围
    /// 设置后会在 VideoToolboxDecode 中根据 DynamicRange.availableHDRModes 启用色彩空间转换
    /// 用于不同设备对HDR的支持情况，比如外置屏幕不支持HDR，需要转换为SDR
    public var destinationDynamicRange: DynamicRange?

    /// 是否为直播 比如 iptv 需要启用来避免缓存过多，因为直播的缓存是没有意义的
    public let isLive = false

    /// 视频队列中最大放入多少帧
    public func videoFrameMaxCount(fps: Float) -> UInt8 {
        if isLive {
            return fps > 50 ? 8 : 4
        } else {
            return 16
        }
    }

    /// 音频队列中最大放入多少帧
    public func audioFrameMaxCount(fps: Float, channelCount: Int) -> UInt8 {
        let count = (Int(fps) * channelCount) >> 2 // 计算音频帧数
        if count >= UInt8.max {
            return UInt8.max
        } else {
            return UInt8(count)
        }
    }

    /// 添加去交错过滤器
    /// 调用需要放置在创建解码器之前
    /// 判断fieldOrder为tt或bb
    public func deInterlaceFilter(assetTrack: some FFmpegStreamAsset) {
        if assetTrack.mediaType == .video {
            if [FFmpegFieldOrder.bb, .bt, .tt, .tb].contains(assetTrack.fieldOrder) {
                // 禁用videoToolBox解码 使用FFMpeg解码器
                videoToolBoxDecode = false
                // 判断ffmpeg软硬解码选择过滤器版本
                let yadif = ffmpegVTDecode ? "yadif_videotoolbox" : "yadif"
                if !ffmpegVTDecode {
                    // 视频场判断，用于搭配yadif，没有vt硬解版本
                    videoFilters.append("idet")
                }
                // yadifMode 的可选值包括：
                // 0：每帧输出一帧（使用当前帧进行去交错）。
                // 1：每个场输出一帧，搭配idet。
                // 2：和0一样，但跳过了空间隔行检查。
                // 3：和1一样，但跳过了空间隔行检查。
                let yadifMode = 1
                videoFilters.append("\(yadif)=mode=\(yadifMode)") // 添加yadif过滤器
                if yadifMode == 1 || yadifMode == 3 {
                    assetTrack.nominalFrameRate = assetTrack.nominalFrameRate * 2 // 更新帧率
                }
            }
        }
    }

    /// 处理自定义协议
    public var customFileProtocol: CustomAVIOContext?
    init(customFileProtocol: CustomAVIOContext? = nil) {
        self.customFileProtocol = customFileProtocol
    }

    // MARK: 字幕

    static let fontsDir = URL(fileURLWithPath: NSTemporaryDirectory() + "fontsDir")

    // 丢弃掉字幕自带的样式，用自定义的样式
    public static var stripSutitleStyle = false
    public static var textColor: Color = .white
    public static var textBackgroundColor: Color = .clear
    public static var textFont: UIFont {
        textBold ? .boldSystemFont(ofSize: textFontSize) : .systemFont(ofSize: textFontSize)
    }

    public static var textFontSize = SubtitleModel.Size.standard.rawValue
    public static var textBold = false
    public static var textItalic = false
    public static var textPosition = TextPosition()
}

#if canImport(UIKit)
    public extension PlayerOptions {
//        @MainActor
        static var windowScene: UIWindowScene? {
            UIApplication.shared.connectedScenes.first as? UIWindowScene
        }

//        @MainActor
        static var sceneSize: CGSize {
            let window = windowScene?.windows.first
            return window?.bounds.size ?? .zero
        }

        static var scale: CGFloat {
            UITraitCollection.current.displayScale
        }
    }
#else
    public extension PlayerOptions {
        static var sceneSize: CGSize {
            NSScreen.main?.frame.size ?? .zero
        }

        static var scale: CGFloat {
            NSScreen.main?.backingScaleFactor ?? 2
        }
    }
#endif
