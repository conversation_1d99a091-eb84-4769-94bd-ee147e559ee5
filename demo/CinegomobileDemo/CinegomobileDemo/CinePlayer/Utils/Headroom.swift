//
//  Headroom.swift
//  VideoPlayer
//
//  Created by Zero on 2024/9/26.
//

#if canImport(UIKit)
    import UIKit
#else
    import AppKit
#endif

import AVFoundation

// 获取显示器EDR亮度信息
// 大于1.0表示支持HDR
func GetHeadroom(screen: Any?) -> CGFloat {
    var headroom = 1.0
    #if canImport(UIKit)
        if let screen = screen as? UIScreen {
            if screen.currentEDRHeadroom > 1.0 {
                headroom = screen.currentEDRHeadroom
            } else {
                headroom = screen.potentialEDRHeadroom
            }
        }
    #else
        if let screen = screen as? NSScreen {
            if screen.maximumExtendedDynamicRangeColorComponentValue > 1.0 {
                headroom = screen.maximumExtendedDynamicRangeColorComponentValue
            } else {
                headroom = screen.maximumPotentialExtendedDynamicRangeColorComponentValue
            }
        }
    #endif
    return CGFloat(headroom)
}
