//
//  ColorUtils.swift
//  VideoPlayer
//
//  Created by Zero on 2024/10/3.
//

import CFFmpeg
import CoreMedia
import Foundation

public class ColorUtils {
    /// https://chromium.googlesource.com/chromium/src/+/HEAD/media/gpu/mac/vt_config_util.mm
    /// GetPrimaries
    public func GetColorPrimaries(primaryId: AVColorPrimaries) -> CFString? {
        switch primaryId {
        case AVCOL_PRI_UNSPECIFIED, AVCOL_PRI_BT709:
            return kCMFormatDescriptionColorPrimaries_ITU_R_709_2

        case AVCOL_PRI_BT2020:
            return kCMFormatDescriptionColorPrimaries_ITU_R_2020

        case AVCOL_PRI_SMPTE170M, AVCOL_PRI_SMPTE240M:
            return kCMFormatDescriptionColorPrimaries_SMPTE_C

        case AVCOL_PRI_BT470BG, AVCOL_PRI_EBU3213:
            return kCMFormatDescriptionColorPrimaries_EBU_3213

        case AVCOL_PRI_SMPTE431: // SMPTE ST 431-2 (2011) / DCI P3
            return kCMFormatDescriptionColorPrimaries_DCI_P3

        case AVCOL_PRI_SMPTE432: // SMPTE ST 432-1 (2010) / P3 D65 / Display P3
            return kCMFormatDescriptionColorPrimaries_P3_D65

        default:
            return CVColorPrimariesGetStringForIntegerCodePoint(Int32(primaryId.rawValue))?.takeUnretainedValue()
        }
    }

    /// https://chromium.googlesource.com/chromium/src/+/HEAD/media/gpu/mac/vt_config_util.mm
    /// GetTransferFunction
    public func GetTransferFunction(transferId: AVColorTransferCharacteristic) -> CFString? {
        switch transferId {
        case AVCOL_TRC_LINEAR:
            return kCMFormatDescriptionTransferFunction_Linear

        case AVCOL_TRC_GAMMA22, AVCOL_TRC_GAMMA28:
            return kCMFormatDescriptionTransferFunction_UseGamma

        case AVCOL_TRC_IEC61966_2_1:
            return kCMFormatDescriptionTransferFunction_sRGB

        case AVCOL_TRC_BT709, AVCOL_TRC_SMPTE170M, AVCOL_TRC_UNSPECIFIED:
            return kCMFormatDescriptionTransferFunction_ITU_R_709_2

        case AVCOL_TRC_BT2020_10, AVCOL_TRC_BT2020_12:
            return kCMFormatDescriptionTransferFunction_ITU_R_2020

        case AVCOL_TRC_SMPTE240M:
            return kCMFormatDescriptionTransferFunction_SMPTE_240M_1995

        case AVCOL_TRC_SMPTE428:
            return kCMFormatDescriptionTransferFunction_SMPTE_ST_428_1

        case AVCOL_TRC_SMPTE2084:
            return kCMFormatDescriptionTransferFunction_SMPTE_ST_2084_PQ

        case AVCOL_TRC_ARIB_STD_B67:
            return kCMFormatDescriptionTransferFunction_ITU_R_2100_HLG

        default:
            return CVTransferFunctionGetStringForIntegerCodePoint(Int32(transferId.rawValue))?.takeUnretainedValue()
        }
    }

    /// https://chromium.googlesource.com/chromium/src/+/HEAD/media/gpu/mac/vt_config_util.mm
    /// GetMatrix
    public func GetYCbCrMatrix(matrixId: AVColorSpace) -> CFString? {
        switch matrixId {
        case AVCOL_SPC_UNSPECIFIED, AVCOL_SPC_BT709:
            return kCMFormatDescriptionYCbCrMatrix_ITU_R_709_2

        case AVCOL_SPC_BT2020_CL, AVCOL_SPC_BT2020_NCL:
            return kCMFormatDescriptionYCbCrMatrix_ITU_R_2020

        case AVCOL_SPC_FCC, AVCOL_SPC_BT470BG, AVCOL_SPC_SMPTE170M:
            return kCMFormatDescriptionYCbCrMatrix_ITU_R_601_4

        case AVCOL_SPC_SMPTE240M:
            return kCMFormatDescriptionYCbCrMatrix_SMPTE_240M_1995

        default:
            return CVYCbCrMatrixGetStringForIntegerCodePoint(Int32(matrixId.rawValue))?.takeUnretainedValue()
        }
    }

    /// 根据YCbCr矩阵和传输函数返回颜色空间
    public func GetColorSpace(ycbcrMatrix: CFString?, transferFunction: CFString?) -> CGColorSpace? {
        switch ycbcrMatrix {
        case kCVImageBufferYCbCrMatrix_ITU_R_709_2:
            return CGColorSpace(name: CGColorSpace.itur_709)

        case kCVImageBufferYCbCrMatrix_ITU_R_601_4:
            return CGColorSpace(name: CGColorSpace.sRGB)

        case kCVImageBufferYCbCrMatrix_ITU_R_2020:
            if transferFunction == kCVImageBufferTransferFunction_SMPTE_ST_2084_PQ {
                return CGColorSpace(name: CGColorSpace.itur_2100_PQ)

            } else if transferFunction == kCVImageBufferTransferFunction_ITU_R_2100_HLG {
                return CGColorSpace(name: CGColorSpace.itur_2100_HLG)
            } else {
                return CGColorSpace(name: CGColorSpace.itur_2020)
            }

        default:
            return CGColorSpace(name: CGColorSpace.sRGB)
        }
    }

    /// 根据平面数量和位深度返回像素格式数组
    public func GetPixelFormat(planeCount: Int, bitDepth: Int32) -> [MTLPixelFormat] {
        if planeCount == 3 {
            if bitDepth > 8 {
                return [.r16Unorm, .r16Unorm, .r16Unorm]
            } else {
                return [.r8Unorm, .r8Unorm, .r8Unorm]
            }
        } else if planeCount == 2 {
            if bitDepth > 8 {
                return [.r16Unorm, .rg16Unorm]
            } else {
                return [.r8Unorm, .rg8Unorm]
            }
        } else {
            return [GetColorPixelFormat(bitDepth: bitDepth)]
        }
    }

    /// 根据位深度返回颜色像素格式
    public func GetColorPixelFormat(bitDepth: Int32) -> MTLPixelFormat {
        if bitDepth == 10 {
            return .bgr10a2Unorm
        } else {
            return .bgra8Unorm
        }
    }
}
