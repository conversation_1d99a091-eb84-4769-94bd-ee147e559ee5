//
//  CinePlayerUtils.swift
//  VideoPlayer
//
//  Created by Zero on 2024/10/3.
//

import Foundation

@inline(__always)
@preconcurrency
public func runOnMainThread(block: @Sendable @escaping () -> Void) {
    if Thread.isMainThread {
        block()
    } else {
        Task {
            await MainActor.run(body: block)
        }
    }
}

// 性能 while > stride(from:to:by:) > for in
@inline(__always)
func loop(iterations: Int, stride: Int = 1, body: (Int) -> Void) {
    var index = 0
    while index < iterations {
        body(index)
        index += stride
    }
}

/// 将 NSMutableDictionary 转换为字符串
func dictionaryToString(_ dictionary: NSMutableDictionary) -> String {
    var result = "{\n"
    for (key, value) in dictionary {
        result += "  \(key): \(value)\n"
    }
    result += "}"
    return result
}
