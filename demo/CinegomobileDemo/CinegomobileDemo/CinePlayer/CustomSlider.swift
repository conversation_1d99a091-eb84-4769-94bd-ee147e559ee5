import SwiftUI

/// 扩展 Double 类型，添加一个方法用于将当前值从一个范围转换到另一个范围
extension Double {
    func convert(fromRange: (Double, Double), toRange: (Double, Double)) -> Double {
        var value = self
        value -= fromRange.0
        value /= Double(fromRange.1 - fromRange.0)
        value *= toRange.1 - toRange.0
        value += toRange.0
        return value
    }
}

/// 定义一个结构体，用于存储自定义滑块的各个组件
struct CustomSliderComponents {
    let barLeft: CustomSliderModifier
    let barRight: CustomSliderModifier
    let knob: CustomSliderModifier
}

/// 定义一个视图修饰符，用于自定义滑块的外观
struct CustomSliderModifier: ViewModifier {
    enum Name {
        case barLeft
        case barRight
        case knob
    }

    let name: Name
    let size: CGSize
    let offset: CGFloat

    func body(content: Content) -> some View {
        content
            .frame(width: size.width)
            .position(x: size.width * 0.5, y: size.height * 0.5)
            .offset(x: offset)
    }
}

/// 定义一个自定义滑块视图
struct CustomSlider<Component: View>: View {
    @Binding var value: Int

    var range: (Int, Int)
    var knobWidth: CGFloat?
    var onEditingChanged: (Bool) -> Void
    let viewBuilder: (CustomSliderComponents) -> Component

    init(
        value: Binding<Int>,
        range: (Int, Int),
        knobWidth: CGFloat? = nil,
        onEditingChanged: @escaping (Bool) -> Void,
        _ viewBuilder: @escaping (CustomSliderComponents) -> Component
    ) {
        _value = value
        self.range = range
        self.viewBuilder = viewBuilder
        self.knobWidth = knobWidth
        self.onEditingChanged = onEditingChanged
    }

    private func newValue(_ drag: DragGesture.Value, _ frame: CGRect) -> Double {
        let width = (knob: Double(knobWidth ?? frame.size.height), view: Double(frame.size.width))
        let xrange = (min: Double(0), max: Double(width.view - width.knob))
        var value = Double(drag.startLocation.x + drag.translation.width)
        value -= 0.5 * width.knob
        value = value > xrange.max ? xrange.max : value
        value = value < xrange.min ? xrange.min : value
        value = value.convert(fromRange: (xrange.min, xrange.max), toRange: (Double(range.0), Double(range.1)))
        return value
    }

    private func getOffsetX(frame: CGRect) -> CGFloat {
        let width = (knob: knobWidth ?? frame.size.height, view: frame.size.width)
        let xrange: (Double, Double) = (0, Double(width.view - width.knob))
        let result = Double(value).convert(fromRange: (Double(range.0), Double(range.1)), toRange: xrange)
        return CGFloat(result)
    }

    var body: some View {
        GeometryReader { geometry in
            self.view(geometry: geometry)
        }
    }

    private func view(geometry: GeometryProxy) -> some View {
        let frame = geometry.frame(in: .global)
        let drag = DragGesture(minimumDistance: 0)
            .onChanged { drag in
                Task { @MainActor in
                    onEditingChanged(true)
                    // 拖动时更新滑块的值
                    let value = newValue(drag, frame)
                    self.value = Int(value)
                }
            }
            .onEnded { drag in
                Task { @MainActor in
                    let value = newValue(drag, frame)
                    self.value = Int(value)
                    onEditingChanged(false)
                }
            }

        let offsetX = getOffsetX(frame: frame)

        let knobSize = CGSize(width: knobWidth ?? frame.height, height: frame.height)
        let barLeftSize = CGSize(width: CGFloat(offsetX + knobSize.width * 0.5), height: frame.height)
        let barRightSize = CGSize(width: frame.width - barLeftSize.width, height: frame.height)

        let modifiers = CustomSliderComponents(
            barLeft: CustomSliderModifier(name: .barLeft, size: barLeftSize, offset: 0),
            barRight: CustomSliderModifier(name: .barRight, size: barRightSize, offset: barLeftSize.width),
            knob: CustomSliderModifier(name: .knob, size: knobSize, offset: offsetX)
        )

        return ZStack {
            viewBuilder(modifiers)
                .gesture(drag) // 拖动时检测手势
                .contentShape(Rectangle()) // 扩大点击区域
        }
    }
}
