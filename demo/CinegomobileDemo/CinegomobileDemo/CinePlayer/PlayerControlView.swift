import SwiftUI

@MainActor
struct PlayerControlView: View {
    @StateObject private var playerMaskModel = PlayerMaskModel()

    @StateObject
    private var playerCoordinator = CinePlayer.Coordinator()

    var options: PlayerOptions
    var url: URL?
    var presentationMode: Binding<PresentationMode>

    init(options: PlayerOptions, url: URL? = nil, presentationMode: Binding<PresentationMode>) {
        self.options = options
        self.url = url
        self.presentationMode = presentationMode
    }

    // 设置窗口
    @State private var settingTab = 0
    @State private var showSettingContainer = false

    // 字幕窗口
    @State private var subtitleTab = 0
    @State private var showSubtitleContainer = false

    /// 播放列表
    @State private var showPlayListContainer = false
    @State var seasonSelection = 1

    @MainActor
    var body: some View {
        ZStack {
            #if os(macOS)
                MouseTrackingView { _ in
                    self.playerMaskModel.showMask()
                }
            #endif

            // MARK: 播放器视图

            CinePlayer(coordinator: self.playerCoordinator, url: self.url, options: self.options)
            #if os(macOS)
                .edgesIgnoringSafeArea(.bottom)
                .edgesIgnoringSafeArea(.horizontal)
            #else
                .edgesIgnoringSafeArea(.all)
            #endif
                .contentShape(Rectangle())
            #if os(macOS)
                // 双击进入全屏 双击要放在单击之前
                .onTapGesture(count: 2) {
                    guard let view = playerCoordinator.controller?.videoView else {
                        return
                    }
                    view.window?.toggleFullScreen(nil)
                    view.needsLayout = true
                    view.layoutSubtreeIfNeeded()
                }
                .onTapGesture {
                    self.toggleContainer()
                }
            #else
                .onTapGesture {
                        self.toggleContainer()
                        self.playerMaskModel.showMask()
                    }
            #endif
        }
        .overlay {
            GeometryReader { geometry in
                self.controllerView
                self.siderSettingView(geometry: geometry)
            }
        }
        .persistentSystemOverlays(.hidden)
        .preferredColorScheme(.dark)
        .toolbar(.hidden, for: .automatic)
        .onHover { _ in
            self.playerMaskModel.showMask()
        }
        .applyFocusable(true)
        .applyFocusEffectIfNeeded()
        .background(
            Color.black
        )
        .onDisappear {
            self.playerMaskModel.stopTimer()
        }

        // MARK: 键盘控制

        #if os(macOS)
        .onExitCommand {
            self.playerCoordinator.controller?.videoView?.exitFullScreenMode()
        }

        .onAppear {
            NSEvent.addLocalMonitorForEvents(matching: .keyDown) { event in
                // 空格
                if event.keyCode == 49 {
                    if self.playerCoordinator.state.isPlaying {
                        self.playerCoordinator.controller?.pause()
                    } else {
                        self.playerCoordinator.controller?.play()
                    }
                }
                // 左
                if event.keyCode == 123 {
                    self.playerCoordinator.controller?.skip(interval: -15)
                }
                // 右
                if event.keyCode == 124 {
                    self.playerCoordinator.controller?.skip(interval: 15)
                }
                // 上
                if event.keyCode == 126 {
                    if self.playerCoordinator.playbackRate < 4.0 {
                        self.playerCoordinator.playbackRate += 0.25
                    }
                }
                // 下
                if event.keyCode == 125 {
                    if self.playerCoordinator.playbackRate > 0.25 {
                        self.playerCoordinator.playbackRate -= 0.25
                    }
                }
                return event
            }
        }
        #endif
    }

    private func toggleContainer() {
        if showSettingContainer {
            showSettingContainer = false
        }
        if showSubtitleContainer {
            showSubtitleContainer = false
        }
        if showPlayListContainer {
            showPlayListContainer = false
        }
    }

    // MARK: 控制层

    private var controllerView: some View {
        VStack(spacing: 0) {
            // MARK: 标题

            #if !os(macOS)
                HStack {
                    Text("标题区域")
                        .font(.system(size: 12))
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                }
                .frame(height: 24)
            #else
                VStack {}.padding(.bottom)
            #endif

            // 顶部操作区
            HStack {
                HStack(spacing: 16) {
                    // MARK: 退出按钮

                    Button(action: {
                        CinePlayerLog(level: .info, "点击了退出按钮")

                        self.presentationMode.wrappedValue.dismiss()
                        self.playerCoordinator.controller?.pause()

                        // TODO: 有一定几率Crush
                        self.playerCoordinator.controller?.shutdown()

                    }) {
                        Image(systemName: "xmark")
                            .foregroundColor(.white)
                    }
                    .contentShape(Rectangle())
                    .font(.system(size: 20))
                    .buttonStyle(PlainButtonStyle())
                    .applyFocusable(false)

                    // MARK: 视图填充模式

                    Button {
                        self.playerCoordinator.isScaleAspectFill.toggle()
                    } label: {
                        Image(systemName: self.playerCoordinator.isScaleAspectFill ? "rectangle.arrowtriangle.2.inward" : "rectangle.arrowtriangle.2.outward")
                    }
                    .contentShape(Rectangle())
                    .font(.system(size: 20))
                    .buttonStyle(PlainButtonStyle())
                    .applyFocusable(false)

                    // MARK: 倍速按钮

                    MenuView(selection: self.$playerCoordinator.playbackRate) {
                        ForEach([0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0, 2.5, 3.0, 4.0] as [Float]) { value in
                            // 需要有一个变量text。不然会自动加很多0
                            let text = "\(value) x"
                            Text(text).tag(value)
                        }
                    } label: {
                        let text = "\(playerCoordinator.playbackRate) x"
                        Text(text)
                            .font(.system(size: 15))
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .frame(alignment: .center)
                    }
                    .contentShape(Rectangle())
                    .fixedSize(horizontal: true, vertical: false)
                    .applyFocusable(false)
                    .frame(maxWidth: 50)
                    .frame(alignment: .center)
                }
                .frame(height: 44)
                .padding(.horizontal, 16)
                .background(
                    VisualEffectView()
                        .cornerRadius(14)
                )
                #if os(macOS)
                .shadow(radius: 10)
                #endif

                Spacer()

                HStack(spacing: 16) {
                    // MARK: 换源

                    Button(action: {
                        // 换源
                    }) {
                        Text("换源")
                            .font(.system(size: 15))
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    }
                    .contentShape(Rectangle())
                    .buttonStyle(PlainButtonStyle())
                    .applyFocusable(false)

                    // MARK: 字幕

                    Button(action: {
                        withAnimation {
                            self.showSubtitleContainer.toggle()
                        }
                    }) {
                        Image(systemName: "captions.bubble")
                            .foregroundColor(.white)
                    }
                    .contentShape(Rectangle())
                    .font(.system(size: 20))
                    .buttonStyle(PlainButtonStyle())
                    .applyFocusable(false)

                    // MARK: 设置

                    Button(action: {
                        withAnimation {
                            self.showSettingContainer.toggle()
                        }
                    }) {
                        Image(systemName: "gearshape")
                            .foregroundColor(.white)
                    }
                    .contentShape(Rectangle())
                    .font(.system(size: 20))
                    .buttonStyle(PlainButtonStyle())
                    .applyFocusable(false)
                }
                .frame(height: 44)
                .padding(.horizontal, 16)
                .background(
                    VisualEffectView()
                        .cornerRadius(14)
                )
                #if os(macOS)
                .shadow(radius: 10)
                #endif
            }

            Spacer()

            HStack {
                HStack {
                    // MARK: 上一集

                    Button(action: {}) {
                        Image(systemName: "backward.end.fill")
                            .foregroundColor(.white)
                    }
                    .contentShape(Rectangle())
                    .font(.system(size: 20))
                    .buttonStyle(PlainButtonStyle())
                    .applyFocusable(false)

                    Spacer()

                    // MARK: 暂停

                    Button(action: {
                        if self.playerCoordinator.state.isPlaying {
                            self.playerCoordinator.controller?.pause()
                        } else {
                            self.playerCoordinator.controller?.play()
                        }
                    }) {
                        Image(systemName: self.playerCoordinator.state.isPlaying ? "play.fill" : "pause.fill")
                            .foregroundColor(.white)
                    }
                    .contentShape(Rectangle())
                    .font(.system(size: 20))
                    .buttonStyle(PlainButtonStyle())
                    .applyFocusable(false)

                    Spacer()

                    // MARK: 下一集

                    Button(action: {}) {
                        Image(systemName: "forward.end.fill")
                            .foregroundColor(.white)
                    }
                    .contentShape(Rectangle())
                    .font(.system(size: 20))
                    .buttonStyle(PlainButtonStyle())
                    .applyFocusable(false)
                }
                .frame(width: 96)

                Spacer()
                if self.playerCoordinator.controller?.seekable ?? false {
                    // MARK: 进度条

                    ProgressSliderView(coordinator: self.playerCoordinator, progress: self.playerCoordinator.progress)
                        .frame(maxHeight: .infinity)
                    Spacer()
                }

                // MARK: 播放列表

                Button(action: {
                    withAnimation {
                        self.showPlayListContainer.toggle()
                    }
                }) {
                    Image(systemName: "list.bullet")
                        .foregroundColor(.white)
                }
                .contentShape(Rectangle())
                .font(.system(size: 20))
                .buttonStyle(PlainButtonStyle())
                .applyFocusable(false)
            }
            .frame(height: 44)
            .padding(.horizontal, 16)
            .background(
                VisualEffectView()
                    .cornerRadius(14)
            )
            #if os(macOS)
            .shadow(radius: 10)
            #endif
        }
        .preferredColorScheme(.dark)
        .opacity(playerMaskModel.isMaskShow ? 1 : 0)
        #if os(macOS)
            .padding(.bottom)
            .padding(.horizontal)
        #endif
    }

    // MARK: 自定义进度条 View

    @MainActor
    struct ProgressSliderView: View {
        @ObservedObject
        fileprivate var coordinator: CinePlayer.Coordinator

        /// 监听播放进度
        @ObservedObject
        fileprivate var progress: PlayingProgress

        @State private var isHovering = false

        var body: some View {
            HStack {
                Text(self.coordinator.progress.currentTime.toString(for: .minOrHour))
                    .font(.system(size: 14))
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .frame(alignment: .center)
                    .frame(width: 60, alignment: .center)

                Spacer()

                CustomSlider(
                    value: self.$progress.currentTime,
                    range: (0, self.progress.totalTime),
                    knobWidth: 15,
                    onEditingChanged: { onEditingChanged in
                        if onEditingChanged {
                            self.isHovering = true
                            // 拖动进度条的时候暂停
                            self.coordinator.controller?.pause()
                        } else {
                            // 松开进度条后 seek
                            self.coordinator.controller?.seek(time: TimeInterval(self.coordinator.progress.currentTime))
                            #if !os(macOS)
                                withAnimation {
                                    self.isHovering = false
                                }
                            #endif
                        }
                    }
                ) { modifiers in
                    ZStack(alignment: .top) {
                        Circle()
                            .fill(Color.white)
                            .modifier(modifiers.knob) // 锚点

                        // 左边已经进行的部分
                        ZStack {
                            RoundedRectangle(cornerRadius: self.isHovering ? 2.5 : 1.5)
                                .frame(height: self.isHovering ? 5 : 3)
                            // 设置一个可以点击的空白区域，用于增加点击区域
                            Rectangle()
                                .frame(maxHeight: .infinity)
                                .opacity(0.00001)
                        }
                        .modifier(modifiers.barLeft)

                        // 右边未进行的部分
                        ZStack {
                            RoundedRectangle(cornerRadius: self.isHovering ? 2.5 : 1.5)
                                .frame(height: self.isHovering ? 5 : 3)
                                .opacity(0.1)

                            // 设置一个可以点击的空白区域，用于增加点击区域
                            Rectangle()
                                .frame(maxHeight: .infinity)
                                .opacity(0.00001)
                        }
                        .modifier(modifiers.barRight)
                    }
                }
                .onHover { hovering in
                    withAnimation {
                        self.isHovering = hovering
                    }
                }

                Spacer()

                Text(self.coordinator.progress.totalTime.toString(for: .minOrHour))
                    .font(.system(size: 14))
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .frame(width: 60, alignment: .center)
            }
        }
    }

    // MARK: 侧边设置窗口

    private func siderSettingView(geometry: GeometryProxy) -> some View {
        ZStack(alignment: .trailing) {
            // 设置
            ZStack {
                VisualEffectView()
                VStack(spacing: 0) {
                    Picker("", selection: self.$settingTab) {
                        Text("视频").tag(0)
                        Text("音频").tag(1)
                    }
                    .labelsHidden()
                    .pickerStyle(.segmented)
                    .padding()

                    if self.settingTab == 0 {
                        VideoSettingView()
                            .padding(.bottom, geometry.safeAreaInsets.bottom)
                    } else if self.settingTab == 1 {
                        AudioSettingView(coordinator: self.playerCoordinator)
                            .padding(.bottom, geometry.safeAreaInsets.bottom)
                    }

                    Spacer()
                }
            }
            .frame(width: 300)
            .frame(maxHeight: .infinity)
            .offset(x: geometry.safeAreaInsets.trailing + ((geometry.size.width + 300) / 2) - (self.showSettingContainer ? 330 : 0))
            .padding(.top, geometry.safeAreaInsets.top)
            .shadow(radius: 10)
            .animation(.easeInOut, value: self.showSettingContainer)
            .edgesIgnoringSafeArea(.all)

            // MARK: 字幕窗口

            ZStack {
                VisualEffectView()
                VStack {
                    Picker("", selection: self.$subtitleTab) {
                        if let subtitleTracks = playerCoordinator.controller?.tracks(mediaType: .subtitle), !subtitleTracks.isEmpty {
                            Text("内嵌字幕").tag(0)
                        }
                        Text("外挂字幕").tag(1)
                        Text("字幕调整").tag(2)
                    }
                    .labelsHidden()
                    .pickerStyle(.segmented)
                    .padding()

                    if self.subtitleTab == 0 {
                        EmbeddedSubtitleView(coordinator: self.playerCoordinator)
                    } else if self.subtitleTab == 1 {
                        ExternalSubtitleView()
                    } else if self.subtitleTab == 2 {
                        SubtitleAdjustmentView()
                    }

                    Spacer()
                }
            }
            .frame(width: 300)
            .frame(maxHeight: .infinity)
            .offset(x: geometry.safeAreaInsets.trailing + ((geometry.size.width + 300) / 2) - (self.showSubtitleContainer ? 330 : 0))
            .padding(.top, geometry.safeAreaInsets.top)
            .shadow(radius: 10)
            .animation(.easeInOut, value: self.showSubtitleContainer)
            .edgesIgnoringSafeArea(.all)

            // MARK: 播放列表窗口

            ZStack {
                VisualEffectView()
                VStack(alignment: .leading) {
                    SegmentedPickerView(selection: self.$seasonSelection, options: [
                        SegmentedPickerOption(title: "选项1", value: 1),
                        SegmentedPickerOption(title: "选项2", value: 2),
                        SegmentedPickerOption(title: "选项3", value: 3),
                        SegmentedPickerOption(title: "选项4", value: 4),
                        SegmentedPickerOption(title: "选项5", value: 5),
                        SegmentedPickerOption(title: "选项6", value: 6),
                        SegmentedPickerOption(title: "选项7", value: 7),
                    ])
                    .frame(height: 20)
                    .padding(.bottom, 20)

                    VStack {
                        if self.seasonSelection == 1 {
                            Text("播放列表1")
                        } else if self.seasonSelection == 2 {
                            Text("播放列表2")
                        } else if self.seasonSelection == 3 {
                            Text("播放列表3")
                        }
                    }
                    .padding(.horizontal, 20)

                    Spacer()
                }
                .padding(.vertical, 20)
            }
            .frame(width: 360)
            .frame(maxHeight: .infinity)
            .offset(x: geometry.safeAreaInsets.trailing + ((geometry.size.width + 360) / 2) - (self.showPlayListContainer ? 360 : 0))
            .padding(.top, geometry.safeAreaInsets.top)
            .shadow(radius: 10)
            .animation(.easeInOut, value: self.showPlayListContainer)
            .edgesIgnoringSafeArea(.all)
        }
        .frame(width: geometry.size.width)
    }

    // MARK: 视频设置

    @MainActor
    private struct VideoSettingView: View {
        var body: some View {
            Text("这是视频设置")
        }
    }

    // MARK: 音频设置

    @MainActor
    private struct AudioSettingView: View {
        @ObservedObject
        fileprivate var coordinator: CinePlayer.Coordinator

        var body: some View {
            VStack(spacing: 0) {
                if let audioTracks = coordinator.controller?.tracks(mediaType: .audio), !audioTracks.isEmpty {
                    Text("音轨")
                        .font(.system(size: 14))
                        .fontWeight(.medium)
                        .frame(width: 250, height: 36, alignment: .leading)
                    ScrollView {
                        VStack(spacing: 10) {
                            ForEach(audioTracks, id: \.streamIndex) { audioTrack in
                                VStack(alignment: .leading, spacing: 0) {
                                    HStack {
                                        if audioTracks.first(where: { $0.isEnabled })?.streamIndex == audioTrack.streamIndex {
                                            Image(systemName: "lines.measurement.horizontal")
                                                .foregroundColor(.white)
                                        }
                                        Text(audioTrack.description)
                                            .font(.system(size: 14))
                                            .fontWeight(.medium)
                                            .foregroundColor(audioTracks.first { $0.isEnabled }?.streamIndex == audioTrack.streamIndex ? Color.white : Color.gray)
                                    }
                                    .frame(width: 240, height: 36, alignment: .leading)
                                    .padding(.leading, 10)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(audioTracks.first { $0.isEnabled }?.streamIndex == audioTrack.streamIndex ? Color.white : Color.gray, lineWidth: 0.8) // 添加边框和圆角
                                    )
                                }
                                .contentShape(Rectangle()) // 扩大点击区域
                                .onTapGesture {
                                    self.coordinator.controller?.select(track: audioTrack)
                                }
                            }
                        }
                        .padding(.horizontal, 2)
                    }
                    .frame(width: 250)
                }
            }
        }
    }

    // MARK: 内嵌字幕

    @MainActor
    private struct EmbeddedSubtitleView: View {
        @ObservedObject
        fileprivate var coordinator: CinePlayer.Coordinator

        var body: some View {
            if let subtitleTracks = coordinator.controller?.tracks(mediaType: .subtitle), !subtitleTracks.isEmpty {
                ScrollView {
                    VStack(spacing: 10) {
                        ForEach(subtitleTracks, id: \.streamIndex) { subtitleTrack in
                            VStack(alignment: .leading, spacing: 0) {
                                Text(subtitleTrack.description)
                                    .font(.system(size: 14))
                                    .fontWeight(self.coordinator.controller!.currentSubtitleStreamIndex == subtitleTrack.streamIndex ? .medium : .regular)
                                    .frame(width: 240, height: 36, alignment: .leading)
                                    .padding(.leading, 16)
                                    .foregroundColor(self.coordinator.controller!.currentSubtitleStreamIndex == subtitleTrack.streamIndex ? Color.white : Color.gray)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(self.coordinator.controller!.currentSubtitleStreamIndex == subtitleTrack.streamIndex ? Color.white : Color.gray, lineWidth: 0.8) // 添加边框和圆角
                                    )
                            }
                            .contentShape(Rectangle()) // 扩大点击区域
                            .onTapGesture {
                                self.coordinator.controller?.loadSubtitle(subtitlesTrackIndex: subtitleTrack.streamIndex)
                            }
                        }
                    }
                    .padding(.horizontal, 2)
                }
                .frame(width: 250)
            }
        }
    }

    // MARK: 外挂字幕

    @MainActor
    private struct ExternalSubtitleView: View {
        var body: some View {
            Text("外挂字幕页面")
        }
    }

    // MARK: 字幕调整

    @MainActor
    private struct SubtitleAdjustmentView: View {
        var body: some View {
            Text("字幕调整页面")
        }
    }
}

extension View {
    @ViewBuilder
    func applyFocusEffectIfNeeded() -> some View {
        if #available(macOS 14.0, iOS 17.0, *) {
            self.focusEffectDisabled()
        } else {
            self
        }
    }

    @ViewBuilder
    func applyFocusable(_ isFocusable: Bool) -> some View {
        if #available(iOS 17.0, *) {
            self.focusable(isFocusable)
        } else {
            self
        }
    }
}

#if os(macOS)

    // MARK: 鼠标移动监控区

    struct MouseTrackingView: NSViewRepresentable {
        var onMouseMove: (CGPoint) -> Void

        class Coordinator: NSObject {
            var parent: MouseTrackingView

            init(parent: MouseTrackingView) {
                self.parent = parent
            }

            @objc func mouseMoved(with event: NSEvent) {
                let location = event.locationInWindow
                parent.onMouseMove(location)
            }
        }

        func makeCoordinator() -> Coordinator {
            return Coordinator(parent: self)
        }

        func makeNSView(context: Context) -> NSView {
            let view = CustomNSView()
            view.coordinator = context.coordinator
            return view
        }

        func updateNSView(_: NSView, context _: Context) {
            // No update needed
        }

        class CustomNSView: NSView {
            weak var coordinator: Coordinator?

            override func updateTrackingAreas() {
                super.updateTrackingAreas()
                trackingAreas.forEach { self.removeTrackingArea($0) }
                let trackingArea = NSTrackingArea(
                    rect: bounds,
                    options: [.mouseMoved, .activeInKeyWindow, .inVisibleRect],
                    owner: self,
                    userInfo: nil
                )
                addTrackingArea(trackingArea)
            }

            override func mouseMoved(with event: NSEvent) {
                coordinator?.mouseMoved(with: event)
            }
        }
    }
#endif

// MARK: 下拉按钮

public struct MenuView<Label, SelectionValue, Content>: View where Label: View, SelectionValue: Hashable, Content: View {
    public let selection: Binding<SelectionValue>
    @ViewBuilder
    public let content: () -> Content
    @ViewBuilder
    public let label: () -> Label
    @State
    private var showMenu = false
    public var body: some View {
        if #available(tvOS 17, *) {
            Menu {
                Picker(selection: selection) {
                    content()
                } label: {
                    EmptyView()
                }
                .pickerStyle(.inline)
            } label: {
                label()
            }
            .menuIndicator(.hidden)
        } else {
            Picker(selection: selection, content: content, label: label)
            #if !os(macOS)
                .pickerStyle(.navigationLink)
            #endif
                .frame(height: 50)
            #if os(tvOS)
                .frame(width: 110)
            #endif
        }
    }
}
