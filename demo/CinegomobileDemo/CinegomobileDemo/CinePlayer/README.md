# 仓库说明

这个仓库作为所有使用到播放器的项目的公共仓库，用于存放播放器的代码。

通过 git 子模块的方式引入到项目中。

## 使用方式

第一次使用，在项目根目录下执行 

`git <NAME_EMAIL>:cinemore/CinePlayer.git ./项目代码文件夹/CinePlayer`

`git submodule update --init --recursive`

后续更新子模块

`git submodule update --remote --merge`

#  播放器说明

## 控制

通过修改 CinePlayer.Coordinator 中的数值如 isMuted 来控制播放器的行为。

Coordinator 是 ObservableObject 的，数值是 @Published 的


```swift
// 申明时使用 
@ObservedObject
fileprivate var coordinator: CinePlayer.Coordinator

// 就可以在 UI 上使用
coordinator.isMuted
// 监听到变动
```

