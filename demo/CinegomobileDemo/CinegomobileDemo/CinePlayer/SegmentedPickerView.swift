import SwiftUI

struct SegmentedPickerOption {
    let title: String
    let value: Int
}

struct SegmentedPickerView: View {
    @Binding var selection: Int
    @State private var dragOffset: CGFloat = 0
    @State private var lastDragValue: CGFloat = 0
    @State private var totalContentWidth: CGFloat = 0
    let options: [SegmentedPickerOption]

    var body: some View {
        GeometryReader { geometry in
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 10) {
                    ForEach(options, id: \.value) { option in
                        Text(option.title)
                            .font(.system(size: 16))
                            .fontWeight(.semibold)
                            .foregroundColor(selection == option.value ? .white : .gray)
                            .onTapGesture {
                                selection = option.value
                            }
                            .padding(.trailing, 10)
                    }
                }
                .padding(.horizontal, 20)
                .background(GeometryReader { innerGeometry in
                    Color.clear
                        .onAppear {
                            totalContentWidth = innerGeometry.size.width
                        }
                        .onChange(of: innerGeometry.size.width) { newWidth in
                            totalContentWidth = newWidth
                        }
                })
                .offset(x: dragOffset)
            }
            .gesture(
                DragGesture()
                    .onChanged { value in
                        let maxOffset = totalContentWidth - geometry.size.width
                        let newOffset = lastDragValue + value.translation.width
                        dragOffset = min(max(newOffset, -maxOffset), 0)
                    }
                    .onEnded { _ in
                        lastDragValue = dragOffset
                    }
            )
            .simultaneousGesture(
                DragGesture()
                    .onChanged { value in
                        let maxOffset = totalContentWidth - geometry.size.width
                        let newOffset = lastDragValue + value.translation.width
                        dragOffset = min(max(newOffset, -maxOffset), 0)
                    }
                    .onEnded { _ in
                        lastDragValue = dragOffset
                    }
            )
        }
    }
}

// #Preview {
//
//    @Previewable @State var selection = 0
//    return SegmentedPickerView(selection: $selection, options: [
//        SegmentedPickerOption(title: "选项1", value: 1),
//        SegmentedPickerOption(title: "选项2", value: 2),
//        SegmentedPickerOption(title: "选项3", value: 3),
//        SegmentedPickerOption(title: "选项4", value: 4),
//        SegmentedPickerOption(title: "选项5", value: 5),
//        SegmentedPickerOption(title: "选项6", value: 6),
//        SegmentedPickerOption(title: "选项7", value: 7)
//    ])
// }
