import AVFoundation
import Combine
import CoreMedia
import MetalKit
import SwiftUI

@MainActor
public final class VideoView: UIView {
    private var isDovi: Bool = false
    private var formatDescription: CMFormatDescription?

    // MARK: - 设置显示帧率，同步到显示器

    private var fps = Float(60) {
        didSet {
            if fps != oldValue {
                let preferredFramesPerSecond = ceil(fps)
                if #available(iOS 15.0, tvOS 15.0, macOS 14.0, *) {
                    displayLink.preferredFrameRateRange = CAFrameRateRange(minimum: preferredFramesPerSecond, maximum: 2 * preferredFramesPerSecond, __preferred: preferredFramesPerSecond)
                } else {
                    displayLink.preferredFramesPerSecond = Int(preferredFramesPerSecond)
                }
                updateVideo(refreshRate: fps, isDovi: isDovi, formatDescription: formatDescription)
            }
        }
    }

    public private(set) var pixelBuffer: PixelBufferProtocol?

    private var displayLink: CADisplayLink!

    public var options: PlayerOptions

    public weak var renderSource: OutputRenderSourceDelegate?

    private var avSampleBufferDisplayView = AVSampleBufferDisplayView()

    private let metalView = MetalView()

    /// 字幕对象
    public let subtitleModel = SubtitleModel()

    /// swiftUI 字幕显示容器
    public let subtitleVC: UIHostingController<VideoSubtitleView>

    // MARK: - 初始化

    public init(options: PlayerOptions) {
        self.options = options

        subtitleVC = UIHostingController(rootView: VideoSubtitleView(model: subtitleModel))
        subtitleVC.loadView()
        subtitleVC.view.backgroundColor = .clear
        subtitleVC.view.translatesAutoresizingMaskIntoConstraints = false

        super.init(frame: .zero)

        addSub(view: avSampleBufferDisplayView)
        addSub(view: metalView)
        addSubtitleToView(to: self)

        metalView.isHidden = true

        displayLink = CADisplayLink(target: self, selector: #selector(renderFrame))
        displayLink.add(to: .main, forMode: .common)

        pause()
    }

    @available(*, unavailable)
    required init(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    /// 添加字幕到视频视图上
    func addSubtitleToView(to view: UIView) {
        if subtitleVC.view.superview != view {
            view.addSubview(subtitleVC.view)
            let constraints = [
                subtitleVC.view.centerXAnchor.constraint(equalTo: view.centerXAnchor),
                subtitleVC.view.centerYAnchor.constraint(equalTo: view.centerYAnchor),
                subtitleVC.view.widthAnchor.constraint(equalTo: view.widthAnchor),
                subtitleVC.view.heightAnchor.constraint(equalTo: view.heightAnchor),
            ]
            #if os(macOS)
                subtitleVC.sizingOptions = .maxSize
                for constraint in constraints {
                    constraint.priority = .defaultLow
                }
            #endif

            NSLayoutConstraint.activate(constraints)
        }
    }

    // MARK: - 播放控制

    public func play() {
        displayLink.isPaused = false
    }

    public func pause() {
        displayLink.isPaused = true
    }

    // MARK: - 让子视图填充整个视图

    private func addSub(view: UIView) {
        addSubview(view)
        view.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            view.leftAnchor.constraint(equalTo: leftAnchor),
            view.topAnchor.constraint(equalTo: topAnchor),
            view.bottomAnchor.constraint(equalTo: bottomAnchor),
            view.rightAnchor.constraint(equalTo: rightAnchor),
        ])
    }

    // MARK: - 设置内容模式 填充、适应、拉伸

    override public var contentMode: UIViewContentMode {
        didSet {
            metalView.contentMode = contentMode

            switch contentMode {
            case .scaleToFill:
                avSampleBufferDisplayView.displayLayer.videoGravity = .resize
            case .scaleAspectFit, .center:
                avSampleBufferDisplayView.displayLayer.videoGravity = .resizeAspect
            case .scaleAspectFill:
                avSampleBufferDisplayView.displayLayer.videoGravity = .resizeAspectFill
            default:
                break
            }
        }
    }

    // MARK: - 清空缓冲区

    public func flush() {
        pixelBuffer = nil
        if avSampleBufferDisplayView.isHidden {
            metalView.clear()
        } else {
            avSampleBufferDisplayView.displayLayer.flushAndRemoveImage()
        }
    }

    // MARK: - 使无效

    public func invalidate() {
        displayLink.invalidate()
    }

    deinit {
        Task { @MainActor in
            flush()
            subtitleVC.view.removeFromSuperview()
            invalidate()
        }
    }

    // MARK: - 读取下一帧

    public func readNextFrame() {
        draw(force: true)
    }

    // MARK: - displayLink 渲染帧

    @objc private func renderFrame() {
        draw(force: false)
    }

    // MARK: - 绘制帧

    private func draw(force: Bool) {
        autoreleasepool {
            guard let frame = renderSource?.getVideoOutputRender(force: force) else { return }
            pixelBuffer = frame.pixelBuffer
            guard let pixelBuffer else { return }

            isDovi = frame.isDovi
            fps = frame.fps
            let cmtime = frame.cmtime

            let par = pixelBuffer.size
            let sar = pixelBuffer.aspectRatio

            if let cvPixelBuffer = pixelBuffer.cvPixelBuffer {
                // 使用 AVSampleBufferDisplayLayer 渲染
                if avSampleBufferDisplayView.isHidden {
                    avSampleBufferDisplayView.isHidden = false
                    metalView.isHidden = true
                    metalView.clear()
                }

                checkFormatDescription(pixelBuffer: pixelBuffer)

                guard let formatDescription else { return }

                avSampleBufferDisplayView.enqueue(imageBuffer: cvPixelBuffer, formatDescription: formatDescription, time: cmtime)
            } else {
                // 使用 metal 渲染
                if !avSampleBufferDisplayView.isHidden {
                    avSampleBufferDisplayView.isHidden = true
                    metalView.isHidden = false
                    avSampleBufferDisplayView.displayLayer.flushAndRemoveImage()
                }

                let size = CGSize(width: par.width, height: par.height * sar.height / sar.width)

                checkFormatDescription(pixelBuffer: pixelBuffer)

                metalView.metalLayer.edrMetadata = frame.EDRMetadata
                metalView.draw(frame: frame, size: size)
            }
            renderSource?.setVideo(time: cmtime, position: frame.position)
        }
    }

    private func checkFormatDescription(pixelBuffer: PixelBufferProtocol) {
        if formatDescription == nil || !pixelBuffer.matche(formatDescription: formatDescription!) {
            formatDescription = pixelBuffer.formatDescription
        }
    }
}

// @MainActor
public func updateVideo(refreshRate: Float, isDovi: Bool, formatDescription: CMFormatDescription?) {
    #if os(tvOS) || os(visionOS)
        // 快速更改preferredDisplayCriteria，会导致isDisplayModeSwitchInProgress变成true。
        // 例如退出一个视频，然后在3s内重新进入的话。所以不判断isDisplayModeSwitchInProgress了
        guard let displayManager = UIApplication.shared.windows.first?.avDisplayManager,
              displayManager.isDisplayCriteriaMatchingEnabled
        else {
            return
        }
        if let dynamicRange = isDovi ? .dolbyVision : formatDescription?.dynamicRange {
            displayManager.preferredDisplayCriteria = AVDisplayCriteria(refreshRate: refreshRate, videoDynamicRange: dynamicRange.rawValue)
        }
    #endif
}

#if os(macOS)
    import CoreVideo

    /// 实现 CADisplayLink 用于 macOS 帧同步
    class CADisplayLink {
        private let displayLink: CVDisplayLink
        private var runloop: RunLoop?
        private var mode = RunLoop.Mode.default
        public var preferredFramesPerSecond = 60

        public var preferredFrameRateRange: CAFrameRateRange {
            get {
                CAFrameRateRange()
            }
            set {}
        }

        public var timestamp: TimeInterval {
            var timeStamp = CVTimeStamp()
            if CVDisplayLinkGetCurrentTime(displayLink, &timeStamp) == kCVReturnSuccess, (timeStamp.flags & CVTimeStampFlags.hostTimeValid.rawValue) != 0 {
                return TimeInterval(timeStamp.hostTime / NSEC_PER_SEC)
            }
            return 0
        }

        public var duration: TimeInterval {
            CVDisplayLinkGetActualOutputVideoRefreshPeriod(displayLink)
        }

        public var targetTimestamp: TimeInterval {
            duration + timestamp
        }

        public var isPaused: Bool {
            get {
                !CVDisplayLinkIsRunning(displayLink)
            }
            set {
                if newValue {
                    CVDisplayLinkStop(displayLink)
                } else {
                    CVDisplayLinkStart(displayLink)
                }
            }
        }

        public init(target: NSObject, selector: Selector) {
            var displayLink: CVDisplayLink?
            CVDisplayLinkCreateWithActiveCGDisplays(&displayLink)
            self.displayLink = displayLink!
            CVDisplayLinkSetOutputHandler(self.displayLink) { [weak self] _, _, _, _, _ in
                guard let self else { return kCVReturnSuccess }
                self.runloop?.perform(selector, target: target, argument: self, order: 0, modes: [self.mode])
                return kCVReturnSuccess
            }
            CVDisplayLinkStart(self.displayLink)
        }

        public init(block: @escaping (() -> Void)) {
            var displayLink: CVDisplayLink?
            CVDisplayLinkCreateWithActiveCGDisplays(&displayLink)
            self.displayLink = displayLink!
            CVDisplayLinkSetOutputHandler(self.displayLink) { _, _, _, _, _ in
                block()
                return kCVReturnSuccess
            }
            CVDisplayLinkStart(self.displayLink)
        }

        open func add(to runloop: RunLoop, forMode mode: RunLoop.Mode) {
            self.runloop = runloop
            self.mode = mode
        }

        public func invalidate() {
            isPaused = true
            runloop = nil
            CVDisplayLinkSetOutputHandler(displayLink) { _, _, _, _, _ in
                kCVReturnError
            }
        }
    }
#endif
