//
//  MetalView.swift
//  VideoPlayer
//
//  Created by Zero on 2024/10/3.
//

import AVFoundation

#if canImport(UIKit)
    import UIKit
#endif

let displayBox = MetalRenderDisplay()

/// 使 Metal 显示视频帧
/// 用于 ffmpeg 解码输出的视频帧
class MetalView: UIView {
    private let render = MetalRender()
    #if canImport(UIKit)
        override public class var layerClass: AnyClass { CAMetalLayer.self }
    #endif
    var metalLayer: CAMetalLayer {
        layer as! CAMetalLayer
    }

    init() {
        super.init(frame: .zero)
        #if !canImport(UIKit)
            layer = CAMetalLayer()
        #endif
        metalLayer.device = MetalRender.device
        metalLayer.framebufferOnly = true
        #if os(macOS)
            metalLayer.displaySyncEnabled = true
        #endif
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func clear() {
        #if !os(tvOS)
            metalLayer.edrMetadata = nil
        #endif
        if let drawable = metalLayer.nextDrawable() {
            MetalRender.clear(drawable: drawable)
        }
    }

    func draw(frame: VideoVTBFrame, size: CGSize) {
        metalLayer.drawableSize = size
        metalLayer.pixelFormat = ColorUtils().GetColorPixelFormat(bitDepth: frame.pixelBuffer.bitDepth)

        let colorspace = frame.pixelBuffer.colorspace
        if colorspace != nil, colorspace != metalLayer.colorspace {
            metalLayer.colorspace = colorspace
            if colorspace?.name != CGColorSpace.sRGB {
                metalLayer.wantsExtendedDynamicRangeContent = GetHeadroom(screen: window?.screen) > 1.0
            }
        }

        guard let drawable = metalLayer.nextDrawable() else {
            CinePlayerLog(level: .debug, "[video] CAMetalLayer not readyForMoreMediaData")
            return
        }

        MetalRender.draw(frame: frame, display: displayBox, drawable: drawable)
    }
}
