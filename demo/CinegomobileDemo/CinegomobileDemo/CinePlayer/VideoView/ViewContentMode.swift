//
//  ViewContentMode.swift
//  VideoPlayer
//
//  Created by Zero on 2024/10/4.
//

// MARK: - ViewContentMode 内容 填充、适应、拉伸

#if !canImport(UIKit)
    import AppKit
    import CoreMedia
    import IOKit.pwr_mgt

    extension NSView {
        var backingLayer: CALayer? {
            #if !canImport(UIKit)
                wantsLayer = true
            #endif
            return layer
        }

        var cornerRadius: CGFloat {
            get {
                backingLayer?.cornerRadius ?? 0
            }
            set {
                backingLayer?.cornerRadius = newValue
            }
        }
    }

    public extension NSView {
        @objc internal var contentMode: UIViewContentMode {
            get {
                if let contentsGravity = backingLayer?.contentsGravity {
                    switch contentsGravity {
                    case .resize:
                        return .scaleToFill
                    case .resizeAspect:
                        return .scaleAspectFit
                    case .resizeAspectFill:
                        return .scaleAspectFill
                    default:
                        return .scaleAspectFit
                    }
                } else {
                    return .scaleAspectFit
                }
            }
            set {
                switch newValue {
                case .scaleToFill:
                    backingLayer?.contentsGravity = .resize
                case .scaleAspectFit:
                    backingLayer?.contentsGravity = .resizeAspect
                case .scaleAspectFill:
                    backingLayer?.contentsGravity = .resizeAspectFill
                case .center:
                    backingLayer?.contentsGravity = .center
                default:
                    break
                }
            }
        }

        var center: CGPoint {
            CGPoint(x: frame.midX, y: frame.midY)
        }

        var alpha: CGFloat {
            get {
                alphaValue
            }
            set {
                alphaValue = newValue
            }
        }

        var backgroundColor: UIColor? {
            get {
                if let layer, let cgColor = layer.backgroundColor {
                    return UIColor(cgColor: cgColor)
                } else {
                    return nil
                }
            }
            set {
                backingLayer?.backgroundColor = newValue?.cgColor
            }
        }

        var clipsToBounds: Bool {
            get {
                if let layer {
                    return layer.masksToBounds
                } else {
                    return false
                }
            }
            set {
                backingLayer?.masksToBounds = newValue
            }
        }

        class func animate(withDuration duration: TimeInterval, animations: @escaping () -> Void, completion: ((Bool) -> Void)? = nil) {
            CATransaction.begin()
            CATransaction.setAnimationDuration(duration)
            CATransaction.setCompletionBlock {
                completion?(true)
            }
            animations()
            CATransaction.commit()
        }

        class func animate(withDuration duration: TimeInterval, animations: @escaping () -> Void) {
            animate(withDuration: duration, animations: animations, completion: nil)
        }

        func layoutIfNeeded() {
            layer?.layoutIfNeeded()
        }

        func centerRotate(byDegrees: Double) {
            layer?.position = center
            layer?.anchorPoint = CGPoint(x: 0.5, y: 0.5)
            layer?.setAffineTransform(CGAffineTransform(rotationAngle: CGFloat(Double.pi * byDegrees / 180.0)))
        }
    }

    @objc public enum ContentMode: Int {
        case scaleToFill

        case scaleAspectFit // contents scaled to fit with fixed aspect. remainder is transparent

        case scaleAspectFill // contents scaled to fill with fixed aspect. some portion of content may be clipped.

        case redraw // redraw on bounds change (calls -setNeedsDisplay)

        case center // contents remain same size. positioned adjusted.

        case top

        case bottom

        case left

        case right

        case topLeft

        case topRight

        case bottomLeft

        case bottomRight
    }

#endif
