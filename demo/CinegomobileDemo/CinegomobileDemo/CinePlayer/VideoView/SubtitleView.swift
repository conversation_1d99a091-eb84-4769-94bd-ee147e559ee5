//
//  SubtitleView.swift
//  VideoPlayer
//
//  Created by Zero on 2024/10/13.
//

import Foundation
import SwiftUI

public struct VideoSubtitleView: View {
    @ObservedObject
    fileprivate var model: SubtitleModel
    public init(model: SubtitleModel) {
        self.model = model
    }

    public var body: some View {
        ZStack {
            ForEach(model.parts) { part in
                part.view(isHDR: model.isHDR)
            }
        }
        // 禁止字幕视图交互，以免抢占视图的点击事件或其它手势事件
        .allowsHitTesting(false)
        .ignoresSafeArea()
    }

    fileprivate static func imageView(_ image: UIImage) -> some View {
        return Image(uiImage: image)
            .resizable()
    }
}

#if os(macOS)
    public extension Image {
        init(uiImage: UIImage) {
            self.init(nsImage: uiImage)
        }
    }
#endif

private extension SubtitlePart {
//    @MainActor
    func view(isHDR: Bool) -> some View {
        Group {
            switch self.render {
            case let .left(info):
                GeometryReader { geometry in
                    // 不能加scaledToFit。不然的话图片的缩放比率会有问题。
                    let rect = info.displaySize.convert(rect: info.rect, toSize: geometry.size)
                    VideoSubtitleView.imageView(info.image)
                        .if(isHDR) {
                            $0.allowedDynamicRange()
                        }
                        .offset(CGSize(width: rect.origin.x, height: rect.origin.y))
                        .frame(width: rect.width, height: rect.height)
                }
            case let .right(text):
                VStack {
                    let textPosition = self.textPosition ?? PlayerOptions.textPosition
                    if textPosition.verticalAlign == .bottom || textPosition.verticalAlign == .center {
                        Spacer()
                    }
                    text.view
                        .italic(value: PlayerOptions.textItalic)
                        .font(Font(PlayerOptions.textFont))
                        .shadow(color: .black.opacity(0.9), radius: 2, x: 1, y: 1)
                        .foregroundColor(PlayerOptions.textColor)
                        .background(PlayerOptions.textBackgroundColor)
                        .multilineTextAlignment(.center)
                        .alignmentGuide(textPosition.horizontalAlign) {
                            $0[.leading]
                        }
                        .padding(textPosition.edgeInsets)
                    #if !os(tvOS)
                        .textSelection()
                    #endif
                    if textPosition.verticalAlign == .top || textPosition.verticalAlign == .center {
                        Spacer()
                    }
                }
            }
        }
    }
}

public extension View {
    /// Applies the given transform if the given condition evaluates to `true`.
    /// - Parameters:
    ///   - condition: The condition to evaluate.
    ///   - transform: The transform to apply to the source `View`.
    /// - Returns: Either the original `View` or the modified `View` if the condition is `true`.
    @ViewBuilder
    func `if`(_ condition: @autoclosure () -> Bool, transform: (Self) -> some View) -> some View {
        if condition() {
            transform(self)
        } else {
            self
        }
    }

    @ViewBuilder
    func `if`(_ condition: @autoclosure () -> Bool, if ifTransform: (Self) -> some View, else elseTransform: (Self) -> some View) -> some View {
        if condition() {
            ifTransform(self)
        } else {
            elseTransform(self)
        }
    }

    @ViewBuilder
    func ifLet<T: Any>(_ optionalValue: T?, transform: (Self, T) -> some View) -> some View {
        if let value = optionalValue {
            transform(self, value)
        } else {
            self
        }
    }
}

extension View {
    func onKeyPressLeftArrow(action: @escaping () -> Void) -> some View {
        if #available(iOS 17.0, macOS 14.0, tvOS 17.0, *) {
            return onKeyPress(.leftArrow) {
                action()
                return .handled
            }
        } else {
            return self
        }
    }

    func onKeyPressRightArrow(action: @escaping () -> Void) -> some View {
        if #available(iOS 17.0, macOS 14.0, tvOS 17.0, *) {
            return onKeyPress(.rightArrow) {
                action()
                return .handled
            }
        } else {
            return self
        }
    }

    func onKeyPressSapce(action: @escaping () -> Void) -> some View {
        if #available(iOS 17.0, macOS 14.0, tvOS 17.0, *) {
            return onKeyPress(.space) {
                action()
                return .handled
            }
        } else {
            return self
        }
    }

    func allowedDynamicRange() -> some View {
        if #available(iOS 17.0, macOS 14.0, tvOS 17.0, *) {
            return self.allowedDynamicRange(Image.DynamicRange.high)
        } else {
            return self
        }
    }

    #if !os(tvOS)
        func textSelection() -> some View {
            if #available(iOS 15.0, macOS 12.0, *) {
                return self.textSelection(.enabled)
            } else {
                return self
            }
        }
    #endif

    func italic(value: Bool) -> some View {
        if #available(iOS 16.0, macOS 13.0, tvOS 16.0, *) {
            return self.italic(value)
        } else {
            return self
        }
    }

    func ksIgnoresSafeArea() -> some View {
        if #available(iOS 14.0, macOS 11.0, tvOS 14.0, *) {
            return self.ignoresSafeArea()
        } else {
            return self
        }
    }
}
