//
//  AVSampleBufferDisplayView.swift
//  VideoPlayer
//
//  Created by Zero on 2024/10/3.
//

import AVFoundation
import Foundation
#if canImport(UIKit)
    import UIKit
#endif

/// 使用 AVSampleBufferDisplayLayer 显示视频帧
class AVSampleBufferDisplayView: UIView {
    #if canImport(UIKit)
        override public class var layerClass: AnyClass { AVSampleBufferDisplayLayer.self }
    #endif
    var displayLayer: AVSampleBufferDisplayLayer {
        layer as! AVSampleBufferDisplayLayer
    }

    override init(frame: CGRect) {
        super.init(frame: frame)
        #if !canImport(UIKit)
            layer = AVSampleBufferDisplayLayer()
        #endif
        var controlTimebase: CMTimebase?
        CMTimebaseCreateWithSourceClock(allocator: kCFAllocatorDefault, sourceClock: CMClockGetHostTimeClock(), timebaseOut: &controlTimebase)
        if let controlTimebase {
            displayLayer.controlTimebase = controlTimebase
            CMTimebaseSetTime(controlTimebase, time: .zero)
            CMTimebaseSetRate(controlTimebase, rate: 1.0)
        }
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func enqueue(imageBuffer: CVPixelBuffer, formatDescription: CMVideoFormatDescription, time _: CMTime) {
        let timing = CMSampleTimingInfo(duration: .invalid, presentationTimeStamp: .zero, decodeTimeStamp: .invalid)
        var sampleBuffer: CMSampleBuffer?

        CMSampleBufferCreateReadyWithImageBuffer(allocator: kCFAllocatorDefault, imageBuffer: imageBuffer, formatDescription: formatDescription, sampleTiming: [timing], sampleBufferOut: &sampleBuffer)

        if let sampleBuffer {
            if let attachmentsArray = CMSampleBufferGetSampleAttachmentsArray(sampleBuffer, createIfNecessary: true) as? [NSMutableDictionary],
               let dic = attachmentsArray.first
            {
                dic[kCMSampleAttachmentKey_DisplayImmediately] = true
            }

            displayLayer.enqueue(sampleBuffer)
            if displayLayer.requiresFlushToResumeDecoding || displayLayer.status == .failed {
                displayLayer.flush()
            }
        }
    }
}
