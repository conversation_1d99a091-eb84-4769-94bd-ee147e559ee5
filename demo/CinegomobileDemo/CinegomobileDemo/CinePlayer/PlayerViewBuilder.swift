
import AVFoundation
import MediaPlayer
import SwiftUI

public struct DynamicInfoView: View {
    @ObservedObject
    var dynamicInfo: PlayerDynamicInfo
    public var body: some View {
        LabeledContent("Display FPS", value: dynamicInfo.displayFPS, format: .number)
        LabeledContent("Audio Video sync", value: dynamicInfo.audioVideoSyncDiff, format: .number)
        LabeledContent("Dropped Frames", value: dynamicInfo.droppedVideoFrameCount + dynamicInfo.droppedVideoPacketCount, format: .number)
        LabeledContent("Bytes Read", value: dynamicInfo.bytesRead.kmFormatted + "B")
        LabeledContent("Audio bitrate", value: dynamicInfo.audioBitrate.kmFormatted + "bps")
        LabeledContent("Video bitrate", value: dynamicInfo.videoBitrate.kmFormatted + "bps")
    }
}

extension EventModifiers {
    static let none = Self()
}

extension View {
    func then(_ body: (inout Self) -> Void) -> Self {
        var result = self
        body(&result)
        return result
    }
}

extension NSAttributedString {
    var view: some View {
        if #available(macOS 12, iOS 15, tvOS 15, *), !PlayerOptions.stripSutitleStyle {
            Text(AttributedString(self))
        } else {
            Text(string)
        }
    }
}
