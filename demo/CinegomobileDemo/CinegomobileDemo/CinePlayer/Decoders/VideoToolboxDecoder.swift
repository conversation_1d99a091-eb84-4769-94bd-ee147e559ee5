import CFFmpeg
import VideoToolbox

class VideoToolboxDecode: DecodeProtocol {
    private var session: DecompressionSession {
        didSet {
            // 释放旧的session
            VTDecompressionSessionInvalidate(oldValue.decompressionSession)
            lastPosition = 0
            startTime = 0
        }
    }

    private let options: PlayerOptions
    private var startTime = Int64(0)
    private var lastPosition = Int64(0)

    /// 解决从后台切换到前台，解码失败的问题
    private var needDecode = false

    init(options: PlayerOptions, session: DecompressionSession) {
        self.options = options
        self.session = session

        if session.assetTrack.mediaType == .video {
            CinePlayerLog(level: .info, "使用原生 VideoToolbox 解码，视频")
        } else if session.assetTrack.mediaType == .audio {
            CinePlayerLog(level: .info, "使用原生 VideoToolbox 解码，音频")
        }
    }

    func decodeFrame(from packet: Packet, completionHandler: @escaping (Result<CineFrame, Error>) -> Void) {
        if needDecode {
            // 重新解码
            session = DecompressionSession(assetTrack: session.assetTrack, options: options)!
            needDecode = false
        }

        guard let corePacket = packet.corePacket?.pointee, let data = corePacket.data else {
            return
        }

        do {
            var tuple = (data, Int(corePacket.size))

            if let bitStreamFilter = session.assetTrack.bitStreamFilter {
                tuple = try bitStreamFilter.filter(tuple)
            }

            let sampleBuffer = try session.formatDescription.createSampleBuffer(tuple: tuple)

            let flags: VTDecodeFrameFlags = [
                ._EnableAsynchronousDecompression, // 解码器可以异步处理帧，但不强制
                ._EnableTemporalProcessing, // 允许视频解码器延迟帧的处理，在当前播放器相当于丢弃
            ]

            var flagOut = VTDecodeInfoFlags(rawValue: 0)
            let timestamp = packet.timestamp
            let packetFlags = corePacket.flags
            let duration = corePacket.duration
            let size = corePacket.size

            _ = VTDecompressionSessionDecodeFrame(session.decompressionSession, sampleBuffer: sampleBuffer, flags: flags, infoFlagsOut: &flagOut) { [weak self] status, infoFlags, imageBuffer, _, _ in
                guard let self, !infoFlags.contains(.frameDropped) else {
                    return
                }

                guard status == noErr else {
                    if status == kVTInvalidSessionErr || status == kVTVideoDecoderMalfunctionErr || status == kVTVideoDecoderBadDataErr {
                        if packet.isKeyFrame {
                            completionHandler(.failure(NSError(errorCode: .codecVideoReceiveFrame, avErrorCode: status)))
                        } else {
                            // 解决从后台切换到前台，解码失败的问题
                            self.needDecode = true
                        }
                    }
                    return
                }

                guard let imageBuffer else {
                    return
                }

                let frame = VideoVTBFrame(pixelBuffer: imageBuffer, fps: session.assetTrack.nominalFrameRate, isDovi: session.assetTrack.dolbyVisionConfig != nil)

                frame.timebase = session.assetTrack.timebase

                if packet.isKeyFrame, packetFlags & AV_PKT_FLAG_DISCARD != 0, self.lastPosition > 0 {
                    self.startTime = self.lastPosition - timestamp
                }

                self.lastPosition = max(self.lastPosition, timestamp)
                frame.position = packet.position
                frame.timestamp = self.startTime + timestamp
                frame.duration = duration
                frame.size = size
                self.lastPosition += frame.duration
                completionHandler(.success(frame))
            }
        } catch {
            completionHandler(.failure(error))
        }
    }

    func doFlushCodec() {
        lastPosition = 0
        startTime = 0
        VTDecompressionSessionWaitForAsynchronousFrames(session.decompressionSession)
    }

    func shutdown() {
        doFlushCodec()
        VTDecompressionSessionInvalidate(session.decompressionSession)
    }
}

/// 注册VideoToolbox硬件解码器
class DecompressionSession {
    fileprivate let formatDescription: CMFormatDescription
    fileprivate let decompressionSession: VTDecompressionSession
    fileprivate var assetTrack: FFmpegStreamAsset
    init?(assetTrack: FFmpegStreamAsset, options: PlayerOptions) {
        self.assetTrack = assetTrack

        guard let pixelFormatType = assetTrack.pixelFormatType,
              let formatDescription = assetTrack.formatDescription
        else {
            return nil
        }

        self.formatDescription = formatDescription

        #if os(macOS)
            VTRegisterProfessionalVideoWorkflowVideoDecoders()
            VTRegisterSupplementalVideoDecoderIfAvailable(formatDescription.mediaSubType.rawValue)
        #endif

        let attributes: NSMutableDictionary = [
            kCVPixelBufferPixelFormatTypeKey: pixelFormatType,
            kCVPixelBufferMetalCompatibilityKey: true,
            kCVPixelBufferWidthKey: assetTrack.codecpar.width,
            kCVPixelBufferHeightKey: assetTrack.codecpar.height,
            kCVPixelBufferIOSurfacePropertiesKey: NSDictionary(),
        ]

        var session: VTDecompressionSession?
        let status = VTDecompressionSessionCreate(allocator: kCFAllocatorDefault, formatDescription: formatDescription, decoderSpecification: CMFormatDescriptionGetExtensions(formatDescription), imageBufferAttributes: attributes, outputCallback: nil, decompressionSessionOut: &session)

        // 在assetTrack中处理pixelFormatType和formatDescription时可能会缺少信息，会导致session创建失败
        guard status == noErr, let decompressionSession = session else {
            CinePlayerLog(level: .warning, "Can not use codec: \(formatDescription.mediaSubType), error: \(status)")
            // status: see /System/Library/Frameworks/VideoToolbox.framework/Versions/A/Headers/VTErrors.h
            // kVTVideoDecoderUnsupportedDataFormatErr    = -12910,
            // 不用过多在意这里的错误，因为会切换到ffmpeg解码，里面也可以调用硬件解码器
            return nil
        }

        CinePlayerLog(level: .info, "Hardware Decompression Session Create: \(formatDescription.mediaSubType)")

        VTSessionSetProperty(decompressionSession, key: kVTDecompressionPropertyKey_PropagatePerFrameHDRDisplayMetadata,
                             value: kCFBooleanTrue)

        // 如果配置了目标动态范围，进行转换 HDR to other HDR or SDR
        if options.destinationDynamicRange != nil {
            // 像素色彩空间转换
            transferColorSpace(decompressionSession: decompressionSession, destinationDynamicRange: options.destinationDynamicRange!)
        }

        self.decompressionSession = decompressionSession
    }
}

/// 色彩空间转换
private func transferColorSpace(decompressionSession: VTDecompressionSession, destinationDynamicRange: DynamicRange) {
    if !DynamicRange.availableHDRModes.contains(destinationDynamicRange) {
        return
    }
    let pixelTransferProperties = [
        kVTPixelTransferPropertyKey_DestinationColorPrimaries: destinationDynamicRange.colorPrimaries,
        kVTPixelTransferPropertyKey_DestinationTransferFunction: destinationDynamicRange.transferFunction,
        kVTPixelTransferPropertyKey_DestinationYCbCrMatrix: destinationDynamicRange.yCbCrMatrix,
    ]
    VTSessionSetProperty(decompressionSession,
                         key: kVTDecompressionPropertyKey_PixelTransferProperties,
                         value: pixelTransferProperties as CFDictionary)
}

private extension CMFormatDescription {
    func createSampleBuffer(tuple: (UnsafeMutablePointer<UInt8>, Int)) throws -> CMSampleBuffer {
        let (data, size) = tuple
        var blockBuffer: CMBlockBuffer?
        var sampleBuffer: CMSampleBuffer?
        var status = CMBlockBufferCreateWithMemoryBlock(allocator: kCFAllocatorDefault, memoryBlock: data, blockLength: size, blockAllocator: kCFAllocatorNull, customBlockSource: nil, offsetToData: 0, dataLength: size, flags: 0, blockBufferOut: &blockBuffer)
        if status == noErr {
            status = CMSampleBufferCreate(allocator: kCFAllocatorDefault, dataBuffer: blockBuffer, dataReady: true, makeDataReadyCallback: nil, refcon: nil, formatDescription: self, sampleCount: 1, sampleTimingEntryCount: 0, sampleTimingArray: nil, sampleSizeEntryCount: 0, sampleSizeArray: nil, sampleBufferOut: &sampleBuffer)
            if let sampleBuffer {
                return sampleBuffer
            }
        }
        throw NSError(errorCode: .codecVideoReceiveFrame, avErrorCode: status)
    }
}

protocol BitStreamFilter {
    static func filter(_ tuple: (UnsafeMutablePointer<UInt8>, Int)) throws -> (UnsafeMutablePointer<UInt8>, Int)
}

enum Nal3ToNal4BitStreamFilter: BitStreamFilter {
    static func filter(_ tuple: (UnsafeMutablePointer<UInt8>, Int)) throws -> (UnsafeMutablePointer<UInt8>, Int) {
        let (data, size) = tuple
        var ioContext: UnsafeMutablePointer<AVIOContext>?
        let status = avio_open_dyn_buf(&ioContext)
        if status == 0 {
            var nalSize: UInt32 = 0
            let end = data + size
            var nalStart = data
            while nalStart < end {
                nalSize = UInt32(nalStart[0]) << 16 | UInt32(nalStart[1]) << 8 | UInt32(nalStart[2])
                avio_wb32(ioContext, nalSize)
                nalStart += 3
                avio_write(ioContext, nalStart, Int32(nalSize))
                nalStart += Int(nalSize)
            }
            var demuxBuffer: UnsafeMutablePointer<UInt8>?
            let demuxSze = avio_close_dyn_buf(ioContext, &demuxBuffer)
            if let demuxBuffer {
                return (demuxBuffer, Int(demuxSze))
            } else {
                throw NSError(errorCode: .codecVideoReceiveFrame, avErrorCode: status)
            }
        } else {
            throw NSError(errorCode: .codecVideoReceiveFrame, avErrorCode: status)
        }
    }
}

enum AnnexbToCCBitStreamFilter: BitStreamFilter {
    static func filter(_ tuple: (UnsafeMutablePointer<UInt8>, Int)) throws -> (UnsafeMutablePointer<UInt8>, Int) {
        let (data, size) = tuple
        var ioContext: UnsafeMutablePointer<AVIOContext>?
        let status = avio_open_dyn_buf(&ioContext)
        if status == 0 {
            var nalStart = data
            var i = 0
            var start = 0
            while i < size {
                if i + 2 < size, data[i] == 0x00, data[i + 1] == 0x00, data[i + 2] == 0x01 {
                    if start == 0 {
                        start = 3
                        nalStart += 3
                    } else {
                        let len = i - start
                        avio_wb32(ioContext, UInt32(len))
                        avio_write(ioContext, nalStart, Int32(len))
                        start = i + 3
                        nalStart += len + 3
                    }
                    i += 3
                } else if i + 3 < size, data[i] == 0x00, data[i + 1] == 0x00, data[i + 2] == 0x00, data[i + 3] == 0x01 {
                    if start == 0 {
                        start = 4
                        nalStart += 4
                    } else {
                        let len = i - start
                        avio_wb32(ioContext, UInt32(len))
                        avio_write(ioContext, nalStart, Int32(len))
                        start = i + 4
                        nalStart += len + 4
                    }
                    i += 4
                } else {
                    i += 1
                }
            }
            let len = size - start
            avio_wb32(ioContext, UInt32(len))
            avio_write(ioContext, nalStart, Int32(len))
            var demuxBuffer: UnsafeMutablePointer<UInt8>?
            let demuxSze = avio_close_dyn_buf(ioContext, &demuxBuffer)
            if let demuxBuffer {
                return (demuxBuffer, Int(demuxSze))
            } else {
                throw NSError(errorCode: .codecVideoReceiveFrame, avErrorCode: status)
            }
        } else {
            throw NSError(errorCode: .codecVideoReceiveFrame, avErrorCode: status)
        }
    }
}

extension CMVideoCodecType {
    /// Configuration Box
    /// 在视频编码和封装中，VPCC、AVCC、HVCC 和 AV1C 是不同类型的配置盒
    var configurationBox: String {
        switch self {
        case kCMVideoCodecType_MPEG4Video:
            return "esds"
        case kCMVideoCodecType_H264:
            return "avcC"
        case kCMVideoCodecType_HEVC:
            return "hvcC"
        case kCMVideoCodecType_VP9:
            return "vpcC"
        case kCMVideoCodecType_AV1:
            return "av1C"
        default: return "avcC"
        }
    }
}
