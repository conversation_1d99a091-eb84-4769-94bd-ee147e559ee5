import AVFoundation
import CFFmpeg
import Foundation

/// 定义一个解码协议
protocol DecodeProtocol: AnyObject {
    /// 解码帧
    func decodeFrame(from packet: Packet, completionHandler: @escaping (Result<CineFrame, Error>) -> Void)

    /// 刷新解码器
    func doFlushCodec()

    /// 关闭解码器
    func shutdown()
}

class FFmpegDecode: DecodeProtocol {
    private let options: PlayerOptions
    private var coreFrame: UnsafeMutablePointer<AVFrame>? = av_frame_alloc()
    private var codecContext: UnsafeMutablePointer<AVCodecContext>?
    private var bestEffortTimestamp = Int64(0)
    private let frameChange: FrameChange
    private let filter: CineFilter

    /// 是否解码结束
    private var isDecodeEnd = false

    /// 解决 频繁 seek 多次后解码失败
    private var errorCount = 0

    required init(assetTrack: FFmpegStreamAsset, options: PlayerOptions) {
        self.options = options
        do {
            codecContext = try assetTrack.createContext(options: options)
        } catch {
            CinePlayerLog(level: .error, error as CustomStringConvertible)
        }

        codecContext?.pointee.time_base = assetTrack.timebase.rational

        filter = CineFilter(timebase: assetTrack.timebase, isAudio: assetTrack.mediaType == .audio, nominalFrameRate: assetTrack.nominalFrameRate, options: options)

        if assetTrack.mediaType == .video {
            frameChange = VideoSwresample(fps: assetTrack.nominalFrameRate, isDovi: assetTrack.dolbyVisionConfig != nil)
        } else {
            frameChange = AudioSwresample(audioDescriptor: assetTrack.audioDescriptor!)
        }

        if assetTrack.mediaType == .video {
            CinePlayerLog(level: .info, "使用 FFmpeg \(options.ffmpegVTDecode ? "VideoToolbox 硬" : "软")解，视频")
        } else if assetTrack.mediaType == .audio {
            CinePlayerLog(level: .info, "使用 FFmpeg 解码，音频")
        } else {
            CinePlayerLog(level: .info, "使用 FFmpeg 解码，\(assetTrack.mediaType)")
        }
    }

    /// 从 packet 中解码出一个帧
    func decodeFrame(from packet: Packet, completionHandler: @escaping (Result<CineFrame, Error>) -> Void) {
        guard let codecContext else {
            return
        }
        if isDecodeEnd {
            return
        }

        let sendResult = avcodec_send_packet(codecContext, packet.corePacket)
        if sendResult != 0 {
            // 清空 ffmpeg 缓存
            doFlushCodec()

            // 处理硬解失败
            if options.ffmpegVTDecode {
                // 有时候频繁快 seek 会出现这两个错误，不需要处理
                // return 丢弃当前 packet
                // -35 Resource temporarily unavailable
                // -1313558101 Unknown error occurred
                // -542398533 Generic error in an external library
                if sendResult == -35 || sendResult == -1_313_558_101 || sendResult == -542_398_533 {
                    return
                } else {
                    CinePlayerLog("ffmpeg send packet error: \(sendResult) \(AVError(code: sendResult).message)。errorCount: \(errorCount)")
                }

                if errorCount <= 30 {
                    errorCount += 1
                    // 继续使用硬解创建 ctx
                    self.codecContext = try? packet.assetTrack.createContext(options: options)
                    return
                }

                // 多次失败使用软解
                options.ffmpegVTDecode = false
                self.codecContext = try? packet.assetTrack.createContext(options: options)
                return
            }

            if errorCount <= 30 {
                errorCount += 1
                return
            }

            // 软解都出错就没办法了
            let error = NSError(errorCode: packet.assetTrack.mediaType == .audio ? .codecAudioSendPacket : .codecVideoSendPacket, avErrorCode: sendResult)
            completionHandler(.failure(error))
            return
        }

        while true {
            if isDecodeEnd {
                return
            }
            let result = avcodec_receive_frame(codecContext, coreFrame)
            if result == 0, let inputFrame = coreFrame {
                // side_data 是一些附加数据，比如杜比视界的元数据，HDR 元数据等

                let doviData: dovi_metadata? = getDoviData(frame: inputFrame)

                // 通过 AVFrame 的 side_data 来生成 EDR matadata
                let displayData = getMasteringDisplayMetadata(frame: inputFrame)
                let contentData = getContentLightMetadata(frame: inputFrame)
                let ambientViewingEnvironment = getAmbientViewingEnvironment(frame: inputFrame)

                // filter.filter()会去处理过滤器，回调是处理完成后的 avframe，以下包裹的是对 avframe的附加处理
                filter.filter(options: options, inputFrame: inputFrame) { avframe in
                    do {
                        var frame = try frameChange.toCineFrame(avframe: avframe)

                        if let videoFrame = frame as? VideoVTBFrame {
                            // 把附加数据赋值给帧，HDR 的亮度信息等
                            if displayData != nil || contentData != nil || ambientViewingEnvironment != nil {
                                videoFrame.hdrMetadata = HDRMetaData(masteringDisplayMetadata: displayData,
                                                                     contentLightMetadata: contentData,
                                                                     ambientViewingEnvironment: ambientViewingEnvironment)
                            }
                            videoFrame.doviData = doviData
                            if let pixelBuffer = videoFrame.pixelBuffer as? PixelBuffer {
                                pixelBuffer.formatDescription = packet.assetTrack.formatDescription
                            }
                        }

                        frame.timebase = filter.timebase
                        frame.size = packet.size
                        frame.position = packet.position
                        frame.duration = avframe.pointee.duration

                        if frame.duration == 0, avframe.pointee.sample_rate != 0, frame.timebase.num != 0 {
                            frame.duration = Int64(avframe.pointee.nb_samples) * Int64(frame.timebase.den) / (Int64(avframe.pointee.sample_rate) * Int64(frame.timebase.num))
                        }
                        var timestamp = avframe.pointee.best_effort_timestamp
                        if timestamp < 0 {
                            timestamp = avframe.pointee.pts
                        }
                        if timestamp < 0 {
                            timestamp = avframe.pointee.pkt_dts
                        }
                        if timestamp < 0 {
                            timestamp = bestEffortTimestamp
                        }

                        frame.timestamp = timestamp
                        bestEffortTimestamp = timestamp &+ frame.duration

                        // 解码成功，清空错误次数
                        errorCount = 0
                        completionHandler(.success(frame))
                    } catch {
                        completionHandler(.failure(error))
                    }
                }
            } else {
                if result == AVError.eof.code {
                    // 已经到文件末尾，需要清空下，不然解码可能还会有缓存，导致返回的数据是之前seek的。
                    avcodec_flush_buffers(codecContext)
                    break
                } else if result == AVError.tryAgain.code {
                    // 需要发送更多的数据进行解码，是给队列塞数据再调用本函数的decodeFrame，所以需要break终止本次帧解码
                    break
                } else {
                    if errorCount <= 30 {
                        errorCount += 1
                        break
                    }

                    // 解码失败清空ctx
                    doFlushCodec()

                    // 一般是ffmpeg使用videoToolBox解码时，解码失败，切换解码器
                    let error = NSError(errorCode: packet.assetTrack.mediaType == .audio ? .codecAudioReceiveFrame : .codecVideoReceiveFrame, avErrorCode: result)
                    // 返回失败回调，让调用者切换解码器
                    completionHandler(.failure(error))

                    break
                }
            }
        }
    }

    func doFlushCodec() {
        bestEffortTimestamp = Int64(0)
        // seek之后要清空下，不然解码可能还会有缓存，导致返回的数据是之前seek的。
        // TODO: shutdown 的时候这里会crash
        if codecContext != nil {
//            avcodec_flush_buffers(codecContext)
        }
    }

    func shutdown() {
        isDecodeEnd = true
        errorCount = 0
        av_frame_free(&coreFrame)
        avcodec_free_context(&codecContext)
        frameChange.shutdown()
    }
}

/// 此代码块用于放置 sidedata 的处理
extension FFmpegDecode {
    // sidedata type
    // https://raw.githubusercontent.com/FFmpeg/FFmpeg/refs/heads/master/libavutil/frame.h

    private func getMasteringDisplayMetadata(frame: UnsafeMutablePointer<AVFrame>) -> MasteringDisplayMetadata? {
        var displayData: MasteringDisplayMetadata?
        // AV_FRAME_DATA_MASTERING_DISPLAY_METADATA
        // 包含有关母带显示颜色量的信息，以及最高最低亮度
        // AVMasteringDisplayMetadata 结构形式的数据。
        if let sideData = av_frame_side_data_get(frame.pointee.side_data, frame.pointee.nb_side_data, AV_FRAME_DATA_MASTERING_DISPLAY_METADATA) {
            // 读取数据
            let data = sideData.pointee.data.withMemoryRebound(to: AVMasteringDisplayMetadata.self, capacity: 1) { $0 }.pointee

            displayData = MasteringDisplayMetadata(
                display_primaries_r_x: data.display_primaries.0.0.float,
                display_primaries_r_y: data.display_primaries.0.1.float,
                display_primaries_g_x: data.display_primaries.1.0.float,
                display_primaries_g_y: data.display_primaries.1.1.float,
                display_primaries_b_x: data.display_primaries.2.1.float,
                display_primaries_b_y: data.display_primaries.2.1.float,
                white_point_x: data.white_point.0.float,
                white_point_y: data.white_point.1.float,
                min_luminance: data.min_luminance.float,
                max_luminance: data.max_luminance.float
            )
        }
        return displayData
    }

    private func getContentLightMetadata(frame: UnsafeMutablePointer<AVFrame>) -> ContentLightMetadata? {
        var contentData: ContentLightMetadata?
        // AV_FRAME_DATA_CONTENT_LIGHT_LEVEL
        // 内容亮度级别（基于 CTA-861.3）。
        // AVContentLightMetadata 结构形式的数据。
        if let sideData = av_frame_side_data_get(frame.pointee.side_data, frame.pointee.nb_side_data, AV_FRAME_DATA_CONTENT_LIGHT_LEVEL) {
            // 读取数据
            let data = sideData.pointee.data.withMemoryRebound(to: AVContentLightMetadata.self, capacity: 1) { $0 }.pointee

            contentData = ContentLightMetadata(
                MaxCLL: UInt16(data.MaxCLL),
                MaxFALL: UInt16(data.MaxFALL)
            )
        }
        return contentData
    }

    private func getAmbientViewingEnvironment(frame: UnsafeMutablePointer<AVFrame>) -> AmbientViewingEnvironment? {
        var ambientViewingEnvironment: AmbientViewingEnvironment?
        // AV_FRAME_DATA_AMBIENT_VIEWING_ENVIRONMENT
        // 包含有关观看环境的信息。
        // AVAmbientViewingEnvironment 结构形式的数据
        if let sideData = av_frame_side_data_get(frame.pointee.side_data, frame.pointee.nb_side_data, AV_FRAME_DATA_AMBIENT_VIEWING_ENVIRONMENT) {
            // 读取数据
            let data = sideData.pointee.data.withMemoryRebound(to: AVAmbientViewingEnvironment.self, capacity: 1) { $0 }.pointee

            ambientViewingEnvironment = AmbientViewingEnvironment(
                ambient_illuminance: data.ambient_illuminance.float,
                ambient_light_x: data.ambient_light_x.float,
                ambient_light_y: data.ambient_light_y.float
            )
        }

        return ambientViewingEnvironment
    }

    /// AV_FRAME_DATA_DOVI_METADATA
    /// 从原始数据解析的杜比视界元数据，适合传递给软件实现。
    /// AVDOVIMetadata 结构形式的数据。
    private func getDoviData(frame: UnsafeMutablePointer<AVFrame>) -> dovi_metadata? {
        if let sideData = av_frame_side_data_get(frame.pointee.side_data, frame.pointee.nb_side_data, AV_FRAME_DATA_DOVI_METADATA) {
            let data = sideData.pointee.data.withMemoryRebound(to: AVDOVIMetadata.self, capacity: 1) { $0 }
            return map_dovi_metadata(data).pointee
        }
        return nil
    }

    // TODO: 双层杜比相关
    // AV_FRAME_DATA_DOVI_RPU_BUFFER
    // 杜比视界 RPU 原始数据，适合传递到 x265 或其他库。 uint8_t 数组，NAL 模拟字节完好无损。
//    private func convertDoviRpu(frame: UnsafeMutablePointer<AVFrame>) {
//        if let sideData = av_frame_side_data_get(frame.pointee.side_data, frame.pointee.nb_side_data, AV_FRAME_DATA_DOVI_RPU_BUFFER) {
//            if let rpu = dovi_parse_unspec62_nalu(sideData.pointee.data, sideData.pointee.size) {
//                defer {
//                    dovi_rpu_free(rpu)
//                }
//
//                if let header = dovi_rpu_get_header(rpu) {
//                    defer {
//                        dovi_rpu_free_header(header)
//                    }
//
//                    // Sets the mode for RPU processing.
//                    // Default (no mode) - Copies the RPU untouched.
//                    // 0 - Parses the RPU, rewrites it untouched.
//                    // 1 - Converts the RPU to be MEL compatible.
//                    // 2 - Converts the RPU to be profile 8.1 compatible. Removes luma/chroma mapping for profile 7 FEL.
//                    // 3 - Converts profile 5 to 8.1.
//                    // 4 - Converts to profile 8.4.
//                    // 5 - Converts to profile 8.1, preserving mapping.
//
//                    if header.pointee.guessed_profile == 5 {
//                        let ret = dovi_convert_rpu_with_mode(rpu, 3)
//                        if ret < 0 {
//                            CinePlayerLog(level: .warning, "dovi RPU转换失败: \(ret)")
//                        }
//                    } else if header.pointee.guessed_profile == 7 {
//                        let ret = dovi_convert_rpu_with_mode(rpu, 2)
//                        if ret < 0 {
//                            CinePlayerLog(level: .warning, "dovi RPU转换失败: \(ret)")
//                        }
//                    }
//                }
//            }
//        }
//    }

    // AV_FRAME_DATA_DYNAMIC_HDR_PLUS
    // HDR Plus 动态元数据，包含颜色体积变换的信息。
    // AVDynamicHDRPlus 结构形式的数据
    // av_frame_side_data_get(frame.pointee.side_data, frame.pointee.nb_side_data, AV_FRAME_DATA_DYNAMIC_HDR_PLUS)
    // let data = sideData.pointee.data.withMemoryRebound(to: AVDynamicHDRPlus.self, capacity: 1) { $0 }.pointee

    // AV_FRAME_DATA_DYNAMIC_HDR_VIVID
    // HDR Vivid 动态元数据。包含色彩体积变换的信息- CUVA 005.1-2021。
    // AVDynamicHDRVivid 结构形式的数据
    // av_frame_side_data_get(frame.pointee.side_data, frame.pointee.nb_side_data, AV_FRAME_DATA_DYNAMIC_HDR_VIVID)
    // let data = sideData.data.withMemoryRebound(to: AVDynamicHDRVivid.self, capacity: 1) { $0 }.pointee

    // AV_FRAME_DATA_SEI_UNREGISTERED 用户数据未注册元数据，没用

    // AV_FRAME_DATA_A53_CC   当前帧字幕
}
