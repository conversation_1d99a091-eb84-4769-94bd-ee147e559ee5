import CFFmpeg
import Foundation

/// 用于处理音频或视频过滤
/// 音频变速、混音等
class CineFilter {
    // 私有变量，用于存储过滤图、源和目标上下文
    private var graph: UnsafeMutablePointer<AVFilterGraph>?
    private var bufferSrcContext: UnsafeMutablePointer<AVFilterContext>?
    private var bufferSinkContext: UnsafeMutablePointer<AVFilterContext>?
    private var filters: String?
    let timebase: Timebase
    private let isAudio: Bool
    private var format = Int32(0)
    private var height = Int32(0)
    private var width = Int32(0)
    private let nominalFrameRate: Float

    /// 析构函数，释放过滤图
    deinit {
        graph?.pointee.opaque = nil
        avfilter_graph_free(&graph)
    }

    /// 初始化函数，设置时间基、是否为音频和名义帧率
    public init(timebase: Timebase, isAudio: Bool, nominalFrameRate: Float, options: PlayerOptions) {
        graph = avfilter_graph_alloc()
        graph?.pointee.opaque = Unmanaged.passUnretained(options).toOpaque()
        self.timebase = timebase
        self.isAudio = isAudio
        self.nominalFrameRate = nominalFrameRate
    }

    /// 设置过滤器图
    private func setup(filters: String, params: UnsafeMutablePointer<AVBufferSrcParameters>?) -> Bool {
        var inputs = avfilter_inout_alloc()
        var outputs = avfilter_inout_alloc()
        var ret = avfilter_graph_parse2(graph, filters, &inputs, &outputs)
        guard ret >= 0, let graph, let inputs, let outputs else {
            avfilter_inout_free(&inputs)
            avfilter_inout_free(&outputs)
            return false
        }
        let bufferSink = avfilter_get_by_name(isAudio ? "abuffersink" : "buffersink")
        ret = avfilter_graph_create_filter(&bufferSinkContext, bufferSink, "out", nil, nil, graph)
        guard ret >= 0 else { return false }
        ret = avfilter_link(outputs.pointee.filter_ctx, UInt32(outputs.pointee.pad_idx), bufferSinkContext, 0)
        guard ret >= 0 else { return false }
        let buffer = avfilter_get_by_name(isAudio ? "abuffer" : "buffer")
        bufferSrcContext = avfilter_graph_alloc_filter(graph, buffer, "in")
        guard bufferSrcContext != nil else { return false }

        av_buffersrc_parameters_set(bufferSrcContext, params)

        ret = avfilter_init_str(bufferSrcContext, nil)
        guard ret >= 0 else { return false }

        ret = avfilter_link(bufferSrcContext, 0, inputs.pointee.filter_ctx, UInt32(inputs.pointee.pad_idx))
        guard ret >= 0 else { return false }

        ret = avfilter_graph_config(graph, nil)
        guard ret >= 0 else { return false }
        return true
    }

    /// 过滤函数，处理输入帧并调用完成处理程序
    /// 音频用于倍速播放等
    public func filter(options: PlayerOptions, inputFrame: UnsafeMutablePointer<AVFrame>, completionHandler: (UnsafeMutablePointer<AVFrame>) -> Void) {
        let filters: String
        if isAudio {
            filters = options.audioFilters.joined(separator: ",")
        } else {
            filters = options.videoFilters.joined(separator: ",")
        }
        guard !filters.isEmpty else {
            completionHandler(inputFrame)
            return
        }

        if format != inputFrame.pointee.format || height != inputFrame.pointee.height || width != inputFrame.pointee.width || self.filters != filters {
            format = inputFrame.pointee.format
            width = inputFrame.pointee.width
            height = inputFrame.pointee.height
            self.filters = filters
            var params = av_buffersrc_parameters_alloc()
            params?.pointee.format = inputFrame.pointee.format
            params?.pointee.time_base = timebase.rational
            params?.pointee.width = inputFrame.pointee.width
            params?.pointee.height = inputFrame.pointee.height
            params?.pointee.sample_aspect_ratio = inputFrame.pointee.sample_aspect_ratio
            params?.pointee.frame_rate = AVRational(num: 1, den: Int32(nominalFrameRate))
            params?.pointee.sample_rate = inputFrame.pointee.sample_rate
            params?.pointee.ch_layout = inputFrame.pointee.ch_layout
            if let ctx = inputFrame.pointee.hw_frames_ctx {
                params?.pointee.hw_frames_ctx = ctx
            }
            let result = setup(filters: filters, params: params)
            av_freep(&params)
            if !result {
                completionHandler(inputFrame)
                return
            }
        }

        let ret = av_buffersrc_add_frame_flags(bufferSrcContext, inputFrame, 0)
        if ret < 0 {
            return
        }
        while av_buffersink_get_frame_flags(bufferSinkContext, inputFrame, 0) >= 0 {
            completionHandler(inputFrame)
            // 一定要加 av_frame_unref，不然会内存泄漏。
            av_frame_unref(inputFrame)
        }
    }
}
