//
//  DynamicRange.swift
//  VideoPlayer
//
//  Created by Zero on 2024/10/3.
//

import AVFoundation
import CoreMedia
#if canImport(UIKit)
    import UIKit
#else
    import AppKit
#endif

public enum DynamicRange: Int32 {
    case sdr = 0
    case hdr10 = 2
    case hlg = 3
    case dolbyVision = 5

    var isHDR: Bool {
        rawValue > 0
    }

    #if canImport(UIKit)
        var hdrMode: AVPlayer.HDRMode {
            switch self {
            case .sdr:
                // 0 表示不支持HDR
                return AVPlayer.HDRMode(rawValue: 0)
            case .hdr10:
                return .hdr10 // 2
            case .hlg:
                return .hlg // 1
            case .dolbyVision:
                return .dolbyVision // 4
            }
        }
    #endif

    public static var availableHDRModes: [DynamicRange] {
        #if os(macOS)
            if GetHeadroom(screen: NSScreen.main) > 1.0 {
                return [.hdr10]
            } else {
                return [.sdr]
            }
        #else
            let availableHDRModes = AVPlayer.availableHDRModes
            if availableHDRModes == AVPlayer.HDRMode(rawValue: 0) {
                return [.sdr]
            } else {
                var modes = [DynamicRange]()
                if availableHDRModes.contains(.dolbyVision) {
                    modes.append(.dolbyVision)
                }
                if availableHDRModes.contains(.hdr10) {
                    modes.append(.hdr10)
                }
                if availableHDRModes.contains(.hlg) {
                    modes.append(.hlg)
                }
                return modes
            }
        #endif
    }
}

extension DynamicRange: CustomStringConvertible {
    public var description: String {
        switch self {
        case .sdr:
            return "SDR"
        case .hdr10:
            return "HDR10"
        case .hlg:
            return "HLG"
        case .dolbyVision:
            return "Dolby Vision"
        }
    }
}

extension DynamicRange {
    var colorPrimaries: CFString {
        switch self {
        case .sdr:
            return kCVImageBufferColorPrimaries_ITU_R_709_2
        case .hdr10, .hlg, .dolbyVision:
            return kCVImageBufferColorPrimaries_ITU_R_2020
        }
    }

    var transferFunction: CFString {
        switch self {
        case .sdr:
            return kCVImageBufferTransferFunction_ITU_R_709_2
        case .hdr10:
            return kCVImageBufferTransferFunction_SMPTE_ST_2084_PQ
        case .hlg, .dolbyVision:
            return kCVImageBufferTransferFunction_ITU_R_2100_HLG
        }
    }

    var yCbCrMatrix: CFString {
        switch self {
        case .sdr:
            return kCVImageBufferYCbCrMatrix_ITU_R_709_2
        case .hdr10, .hlg, .dolbyVision:
            return kCVImageBufferYCbCrMatrix_ITU_R_2020
        }
    }
}

#if canImport(UIKit)
    extension AVPlayer.HDRMode {
        var dynamicRange: DynamicRange {
            if contains(.dolbyVision) {
                return .dolbyVision
            } else if contains(.hlg) {
                return .hlg
            } else if contains(.hdr10) {
                return .hdr10
            } else {
                return .sdr
            }
        }
    }
#endif
