//
//  TypeAlias.swift
//  VideoPlayer
//
//  Created by Zero on 2024/10/11.
//

import Foundation
import SwiftUI

#if canImport(UIKit)
    import UIKit

    public typealias UIViewType = UIView
    public typealias UIViewContentMode = UIView.ContentMode
#else
    import AppKit

    public typealias UIView = NSView
    public typealias UIApplication = NSApplication
    public typealias UIColor = NSColor
    public typealias UIViewContentMode = ContentMode

    public typealias NSViewType = UIView
    public typealias UIViewRepresentable = NSViewRepresentable
    public typealias UIHostingController = NSHostingController

    public typealias UIImage = NSImage
    public typealias UIFont = NSFont
    public typealias UIFontDescriptor = NSFontDescriptor
    public typealias UIButton = CineButton

    public typealias UIControl = NSControl

    public class CineButton: NSButton {
        private var images = [UIControl.State: UIImage]()
        private var titles = [UIControl.State: String]()
        private var titleColors = [State: UIColor]()
        private var targetActions = [ControlEvents: (AnyObject?, Selector)]()

        override public init(frame frameRect: CGRect) {
            super.init(frame: frameRect)
            isBordered = false
        }

        @available(*, unavailable)
        required init?(coder _: NSCoder) {
            fatalError("init(coder:) has not been implemented")
        }

        public var isSelected: Bool = false {
            didSet {
                update(state: isSelected ? .selected : .normal)
            }
        }

        override public var isEnabled: Bool {
            didSet {
                update(state: isEnabled ? .normal : .disabled)
            }
        }

        open func setImage(_ image: UIImage?, for state: UIControl.State) {
            images[state] = image
            if state == .normal, isEnabled, !isSelected {
                self.image = image
            }
        }

        open func setTitle(_ title: String, for state: UIControl.State) {
            titles[state] = title
            if state == .normal, isEnabled, !isSelected {
                self.title = title
            }
        }

        open func setTitleColor(_ titleColor: UIColor?, for state: UIControl.State) {
            titleColors[state] = titleColor
            if state == .normal, isEnabled, !isSelected {
//            self.titleColor = titleColor
            }
        }

        private func update(state: UIControl.State) {
            if let stateImage = images[state] {
                image = stateImage
            }
            if let stateTitle = titles[state] {
                title = stateTitle
            }
        }

        open func addTarget(_ target: AnyObject?, action: Selector, for controlEvents: ControlEvents) {
            targetActions[controlEvents] = (target, action)
        }

        open func removeTarget(_: AnyObject?, action _: Selector?, for controlEvents: ControlEvents) {
            targetActions.removeValue(forKey: controlEvents)
        }

        override open func updateTrackingAreas() {
            for trackingArea in trackingAreas {
                removeTrackingArea(trackingArea)
            }
            let trackingArea = NSTrackingArea(rect: bounds, options: [.mouseEnteredAndExited, .mouseMoved, .activeInKeyWindow], owner: self, userInfo: nil)
            addTrackingArea(trackingArea)
        }

        override public func mouseDown(with event: NSEvent) {
            super.mouseDown(with: event)
            if let (target, action) = targetActions[.touchUpInside] ?? targetActions[.primaryActionTriggered] {
                _ = target?.perform(action, with: self)
            }
        }

        override public func mouseEntered(with event: NSEvent) {
            super.mouseEntered(with: event)
            if let (target, action) = targetActions[.mouseExited] {
                _ = target?.perform(action, with: self)
            }
        }

        override public func mouseExited(with event: NSEvent) {
            super.mouseExited(with: event)
            if let (target, action) = targetActions[.mouseExited] {
                _ = target?.perform(action, with: self)
            }
        }

        open func sendActions(for controlEvents: ControlEvents) {
            if let (target, action) = targetActions[controlEvents] {
                _ = target?.perform(action, with: self)
            }
        }
    }

    @objc public enum ControlEvents: Int {
        case touchDown
        case touchUpInside
        case touchCancel
        case valueChanged
        case primaryActionTriggered
        case mouseEntered
        case mouseExited
    }

    public extension UIControl {
        struct State: OptionSet {
            public var rawValue: UInt
            public init(rawValue: UInt) { self.rawValue = rawValue }
            public static var normal = State(rawValue: 1 << 0)
            public static var highlighted = State(rawValue: 1 << 1)
            public static var disabled = State(rawValue: 1 << 2)
            public static var selected = State(rawValue: 1 << 3)
            public static var focused = State(rawValue: 1 << 4)
            public static var application = State(rawValue: 1 << 5)
            public static var reserved = State(rawValue: 1 << 6)
        }
    }

    extension UIControl.State: Hashable {}
    public class UILabel: NSTextField {
        override init(frame frameRect: CGRect) {
            super.init(frame: frameRect)
            alignment = .left
            isBordered = false
            isEditable = false
            isSelectable = false
            isBezeled = false
            drawsBackground = false
            focusRingType = .none
            textColor = NSColor.white
        }

        @available(*, unavailable)
        required init?(coder _: NSCoder) {
            fatalError("init(coder:) has not been implemented")
        }
    }

    public extension NSControl {
        var textAlignment: NSTextAlignment {
            get {
                alignment
            }
            set {
                alignment = newValue
            }
        }

        var text: String {
            get {
                stringValue
            }
            set {
                stringValue = newValue
            }
        }

        var attributedText: NSAttributedString? {
            get {
                attributedStringValue
            }
            set {
                attributedStringValue = newValue ?? NSAttributedString()
            }
        }

        var numberOfLines: Int {
            get {
                usesSingleLineMode ? 1 : 0
            }
            set {
                usesSingleLineMode = newValue == 1
            }
        }
    }

#endif
