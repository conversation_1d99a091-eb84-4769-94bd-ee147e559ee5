//
//  Clock.swift
//  VideoPlayer
//
//  Created by Zero on 2024/9/26.
//
import AVFoundation

public struct PlayerClock {
    public var rate = 1.0
    public internal(set) var position = Int64(0)
    public private(set) var lastMediaTime = CACurrentMediaTime()
    public internal(set) var time = CMTime.zero {
        didSet {
            lastMediaTime = CACurrentMediaTime()
        }
    }

    func getTime() -> TimeInterval {
        time.seconds + CACurrentMediaTime() - lastMediaTime
    }
}

public enum ClockProcessType {
    case remain
    case next
    case dropNextFrame
    case dropNextPacket
    case dropGOPPacket
    case flush
    case seek
}
