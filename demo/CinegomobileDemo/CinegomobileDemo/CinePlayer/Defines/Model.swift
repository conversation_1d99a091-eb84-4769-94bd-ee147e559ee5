import AVFoundation
import CFFmpeg
import CoreMedia
#if canImport(UIKit)
    import UIKit
#endif

// 定义一个枚举类型 CineSourceState 表示视频源的不同状态
enum CineSourceState {
    case idle // 空闲状态
    case opening // 正在打开
    case opened // 已打开
    case reading // 正在读取
    case seeking // 正在寻找
    case paused // 已暂停
    case finished // 已完成
    case closed // 已关闭
    case failed // 失败
}

// 定义一个协议 OutputRenderSourceDelegate，表示输出渲染源的代理
@MainActor
public protocol OutputRenderSourceDelegate: AnyObject {
    func getVideoOutputRender(force: Bool) -> VideoVTBFrame? // 获取视频输出渲染
    func getAudioOutputRender() -> AudioFrame? // 获取音频输出渲染
    func setAudio(time: CMTime, position: Int64) // 设置音频时间和位置
    func setVideo(time: CMTime, position: Int64) // 设置视频时间和位置
}

// 定义一个协议 FrameOutput，表示帧输出
public protocol FrameOutput: AnyObject {
    var renderSource: OutputRenderSourceDelegate? { get set } // 渲染源代理
    func pause() // 暂停
    func flush() // 刷新
    func play() // 播放
}

// 定义一个枚举类型 CineCodecState 表示编解码的不同状态
enum CineCodecState {
    case idle // 空闲状态
    case decoding // 正在解码
    case flush // 刷新
    case closed // 已关闭
    case failed // 失败
    case finished // 已完成
}

// 定义一个结构体 Timebase 表示时间基
public struct Timebase {
    static let defaultValue = Timebase(num: 1, den: 1) // 默认时间基
    public let num: Int32 // 分子
    public let den: Int32 // 分母

    // 根据秒数获取位置
    func getPosition(from seconds: TimeInterval) -> Int64 {
        Int64(seconds * TimeInterval(den) / TimeInterval(num))
    }

    // 根据时间戳获取 CMTime
    func cmtime(for timestamp: Int64) -> CMTime {
        CMTime(value: timestamp * Int64(num), timescale: den)
    }
}

// 扩展 Timebase 结构体，增加一些功能
extension Timebase {
    // 获取 AVRational 类型的时间基
    public var rational: AVRational {
        AVRational(num: num, den: den)
    }

    // 初始化方法，根据 AVRational 初始化
    init(_ rational: AVRational) {
        num = rational.num
        den = rational.den
    }
}

// 定义一个类 Packet 表示数据包，继承自 FrameQueueItem
final class Packet: FrameQueueItem {
    var duration: Int64 = 0 // 数据包持续时间
    var timestamp: Int64 = 0 // 数据包时间戳
    var position: Int64 = 0 // 数据包位置
    var size: Int32 = 0 // 数据包大小
    private(set) var corePacket = av_packet_alloc() // 核心数据包

    // 获取时间基
    var timebase: Timebase {
        assetTrack.timebase
    }

    // 判断是否为关键帧
    var isKeyFrame: Bool {
        if let corePacket {
            return corePacket.pointee.flags & AV_PKT_FLAG_KEY == AV_PKT_FLAG_KEY
        } else {
            return false
        }
    }

    // 关联的 FFmpegStreamAsset
    var assetTrack: FFmpegStreamAsset! {
        didSet {
            guard let packet = corePacket?.pointee else {
                return
            }
            timestamp = packet.pts == Int64.min ? packet.dts : packet.pts
            position = packet.pos
            duration = packet.duration
            size = packet.size
        }
    }

    // 析构方法，释放数据包
    deinit {
        av_packet_unref(corePacket)
        av_packet_free(&corePacket)
    }
}
