//
//  DolbyVision.swift
//  VideoPlayer
//
//  Created by Zero on 2024/10/7.
//

import CFFmpeg

public struct DOVIDecoderConfigurationRecord {
    public let dv_version_major: UInt8
    public let dv_version_minor: UInt8
    public let dv_profile: UInt8
    public let dv_level: UInt8
    public let rpu_present_flag: UInt8
    public let el_present_flag: UInt8
    public let bl_present_flag: UInt8
    public let dv_bl_signal_compatibility_id: UInt8
    public let dv_md_compression: UInt8
}

public func getDolbyVisionConfigStr(codecID: AVCodecID, bitDepth: Int32, dolbyVisionConfig: DOVIDecoderConfigurationRecord?) -> String {
    guard let dolbyVisionConfig else {
        return ""
    }

    var dolbyVisionConfigStr = ""
    dolbyVisionConfigStr += "Version \(dolbyVisionConfig.dv_version_major).\(dolbyVisionConfig.dv_version_minor), Profile \(dolbyVisionConfig.dv_profile)"

    // 杜比兼容版本
    if dolbyVisionConfig.dv_bl_signal_compatibility_id != 0 {
        dolbyVisionConfigStr += ".\(dolbyVisionConfig.dv_bl_signal_compatibility_id)"
    }

    var dolbyVisionCodecString = ""

    var dolbyVisionBitstreamProfileTag = ""

    if codecID == AV_CODEC_ID_HEVC {
        dolbyVisionBitstreamProfileTag = "dvhe"
    }

    if codecID == AV_CODEC_ID_AV1 {
        dolbyVisionBitstreamProfileTag = "dav1"
        if bitDepth == 8 {
            dolbyVisionBitstreamProfileTag = "dvav"
        }
    }

    dolbyVisionCodecString = ", \(dolbyVisionBitstreamProfileTag).\(dolbyVisionConfig.dv_profile).\(dolbyVisionConfig.dv_level)"

    dolbyVisionConfigStr += dolbyVisionCodecString

    // 双层杜比视界
    var dolbyVisionDualLayers: [String] = []
    if dolbyVisionConfig.bl_present_flag == 1 {
        dolbyVisionDualLayers.append("BL")
    }
    if dolbyVisionConfig.el_present_flag == 1 {
        dolbyVisionDualLayers.append("EL")
    }
    if dolbyVisionConfig.rpu_present_flag == 1 {
        dolbyVisionDualLayers.append("RPU")
    }

    let dolbyVisionDualLayerString = dolbyVisionDualLayers.joined(separator: "+")
    dolbyVisionConfigStr += ", \(dolbyVisionDualLayerString)"

    return dolbyVisionConfigStr
}
