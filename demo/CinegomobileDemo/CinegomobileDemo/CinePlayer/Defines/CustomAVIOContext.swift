//
//  CustomAVIOContext.swift
//  VideoPlayer
//
//  Created by Zero on 2024/10/9.
//

import CFFmpeg
import Foundation

/// 自定义 IO 协议

public class CustomAVIOContext {
    public let bufferSize: Int32
    public let writable: Bool
    public init(bufferSize: Int32 = 32 * 1024, writable: Bool = false) {
        self.bufferSize = bufferSize
        self.writable = writable
    }

    open func read(buffer _: UnsafeMutablePointer<UInt8>?, size: Int32) -> Int32 {
        size
    }

    open func write(buffer _: UnsafePointer<UInt8>?, size: Int32) -> Int32 {
        size
    }

    /// #define SEEK_SET        0 将文件偏移量设置为偏移量
    /// #define SEEK_CUR        1 将文件偏移量设置为当前加偏移量
    /// #define SEEK_END        2 将文件偏移量设置为文件末尾加上偏移量
    open func seek(offset: Int64, whence _: Int32) -> Int64 {
        offset
    }

    open func fileSize() -> Int64 {
        -1
    }

    open func close() {}
    deinit {}
}

extension CustomAVIOContext {
    func getContext() -> UnsafeMutablePointer<AVIOContext> {
        // 需要持有ioContext，不然会被释放掉,等到shutdown再清空
        avio_alloc_context(av_malloc(Int(bufferSize)), bufferSize, writable ? 1 : 0, Unmanaged.passRetained(self).toOpaque()) { opaque, buffer, size -> Int32 in
            let value = Unmanaged<CustomAVIOContext>.fromOpaque(opaque!).takeUnretainedValue()
            let ret = value.read(buffer: buffer, size: size)
            return Int32(ret)
        } _: { opaque, buffer, size -> Int32 in
            let value = Unmanaged<CustomAVIOContext>.fromOpaque(opaque!).takeUnretainedValue()
            let ret = value.write(buffer: buffer, size: size)
            return Int32(ret)
        } _: { opaque, offset, whence -> Int64 in
            let value = Unmanaged<CustomAVIOContext>.fromOpaque(opaque!).takeUnretainedValue()
            if whence == AVSEEK_SIZE {
                return value.fileSize()
            }
            return value.seek(offset: offset, whence: whence)
        }
    }
}
