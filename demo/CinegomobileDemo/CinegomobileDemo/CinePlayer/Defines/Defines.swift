import AVFoundation
import CFFmpeg
import CoreMedia
import CoreServices

#if canImport(UIKit)
    import UIKit
#else
    import AppKit
#endif

/// 播放器动态信息类，遵循 ObservableObject 协议
public class PlayerDynamicInfo: ObservableObject {
    /// 闭包，用于获取元数据
    private let metadataBlock: () -> [String: String]
    /// 闭包，用于获取已读取的字节数
    private let bytesReadBlock: () -> Int64
    /// 闭包，用于获取音频比特率
    private let audioBitrateBlock: () -> Int
    /// 闭包，用于获取视频比特率
    private let videoBitrateBlock: () -> Int

    /// 元数据属性
    public var metadata: [String: String] {
        metadataBlock()
    }

    /// 已读取的字节数属性
    public var bytesRead: Int64 {
        bytesReadBlock()
    }

    /// 音频比特率属性
    public var audioBitrate: Int {
        audioBitrateBlock()
    }

    /// 视频比特率属性
    public var videoBitrate: Int {
        videoBitrateBlock()
    }

    @Published
    public var displayFPS = 0.0 // 显示帧率
    public var audioVideoSyncDiff = 0.0 // 音视频同步差异
    public var byteRate = Int64(0) // 字节率
    public var droppedVideoFrameCount = UInt32(0) // 丢弃的视频帧数
    public var droppedVideoPacketCount = UInt32(0) // 丢弃的视频包数

    /// 初始化方法
    init(metadata: @escaping () -> [String: String], bytesRead: @escaping () -> Int64, audioBitrate: @escaping () -> Int, videoBitrate: @escaping () -> Int) {
        metadataBlock = metadata
        bytesReadBlock = bytesRead
        audioBitrateBlock = audioBitrate
        videoBitrateBlock = videoBitrate
    }
}

/// 章节结构体
public struct Chapter {
    public let start: TimeInterval // 开始时间
    public let end: TimeInterval // 结束时间
    public let title: String // 标题
}

/// 视频自适应比特率状态结构体
public struct VideoAdaptationState {
    public struct BitRateState {
        let bitRate: Int64 // 比特率
        let time: TimeInterval // 时间
    }

    public let bitRates: [Int64] // 比特率数组
    public let duration: TimeInterval // 持续时间
    public internal(set) var fps: Float // 帧率
    public internal(set) var bitRateStates: [BitRateState] // 比特率状态数组
    public internal(set) var currentPlaybackTime: TimeInterval = 0 // 当前播放时间
    public internal(set) var isPlayable: Bool = false // 是否可播放
    public internal(set) var loadedCount: Int = 0 // 已加载计数
}

/// 缓冲协议
public protocol BufferProtocol {
    var fps: Float { get } // 帧率
    var packetCount: Int { get } // 包计数
    var frameCount: Int { get } // 帧计数
    var frameMaxCount: Int { get } // 最大帧计数
    var isEndOfFile: Bool { get } // 是否文件结束
    var mediaType: AVFoundation.AVMediaType { get } // 媒体类型
}

extension BufferProtocol {
    var loadedTime: TimeInterval {
        TimeInterval(packetCount + frameCount) / TimeInterval(fps) // 已加载时间
    }
}

/// 播放器错误域
public let CinePlayerErrorDomain = "CinePlayer.Error"

/// 播放器错误代码枚举
public enum CinePlayerErrorCode: Int {
    case unknown // 未知错误
    case formatCreate // 格式创建错误
    case formatOpenInput // 格式打开输入错误
    case formatOutputCreate // 格式输出创建错误
    case formatFindStreamInfo // 查找流信息错误
    case readFrame // 读取帧错误
    case codecContextCreate // 编解码器上下文创建错误
    case codecContextSetParam // 编解码器上下文设置参数错误
    case codecContextFindDecoder // 查找解码器错误
    case codesContextOpen // 编解码器上下文打开错误
    case codecVideoSendPacket // 发送视频包错误
    case codecAudioSendPacket // 发送音频包错误
    case codecVideoReceiveFrame // 接收视频帧错误
    case codecAudioReceiveFrame // 接收音频帧错误
    case auidoSwrInit // 音频重采样初始化错误
    case pixelBufferPoolCreate
    case videoTracksUnplayable // 视频轨道不可播放
    case codecSubtitleSendPacket
    case subtitleUnEncoding
    case subtitleUnParse
    case subtitleFormatUnSupport
    case subtitleParamsEmpty
}

extension CinePlayerErrorCode: CustomStringConvertible {
    public var description: String {
        switch self {
        case .formatCreate:
            return "avformat_alloc_context 返回 nil"
        case .formatOpenInput:
            return "avformat 无法打开输入"
        case .formatOutputCreate:
            return "avformat_alloc_output_context2 失败"
        case .formatFindStreamInfo:
            return "avformat_find_stream_info 返回 nil"
        case .codecContextCreate:
            return "avcodec_alloc_context3 返回 nil"
        case .codecContextSetParam:
            return "avcodec 无法设置上下文参数"
        case .codecContextFindDecoder:
            return "avcodec_find_decoder 返回 nil"
        case .codesContextOpen:
            return "codesContext 无法打开"
        case .codecVideoSendPacket:
            return "avcodec 无法发送视频包"
        case .codecAudioSendPacket:
            return "avcodec 无法发送音频包"
        case .codecVideoReceiveFrame:
            return "avcodec 无法接收视频帧"
        case .codecAudioReceiveFrame:
            return "avcodec 无法接收音频帧"
        case .videoTracksUnplayable:
            return "视频轨道不可播放"
        case .auidoSwrInit:
            return "swr_init swrContext 失败"
        case .codecSubtitleSendPacket:
            return "avcodec can't decode subtitle"
        case .subtitleUnEncoding:
            return "Subtitle encoding format is not supported."
        case .subtitleUnParse:
            return "Subtitle parsing error"
        case .subtitleFormatUnSupport:
            return "Current subtitle format is not supported"
        case .subtitleParamsEmpty:
            return "Subtitle Params is empty"
        case .pixelBufferPoolCreate:
            return "pixelBufferPool Create fail"
        default:
            return "未知错误"
        }
    }
}

extension NSError {
    /// 通过错误代码初始化 NSError
    convenience init(errorCode: CinePlayerErrorCode, userInfo: [String: Any] = [:]) {
        var userInfo = userInfo
        userInfo[NSLocalizedDescriptionKey] = errorCode.description
        self.init(domain: CinePlayerErrorDomain, code: errorCode.rawValue, userInfo: userInfo)
    }

    /// 通过描述初始化 NSError
    convenience init(description: String) {
        var userInfo = [String: Any]()
        userInfo[NSLocalizedDescriptionKey] = description
        self.init(domain: CinePlayerErrorDomain, code: 0, userInfo: userInfo)
    }
}

/// 时间类型枚举
public enum TimeType {
    case min // 分钟
    case hour // 小时
    case minOrHour // 分钟或小时
    case millisecond // 毫秒
}

public extension TimeInterval {
    /// 将时间间隔转换为字符串
    func toString(for type: TimeType) -> String {
        Int(ceil(self)).toString(for: type)
    }
}

public extension Int {
    func toString(for type: TimeType) -> String {
        var second = self
        var min = second / 60
        second -= min * 60
        switch type {
        case .min:
            return String(format: "%02d:%02d", min, second)

        case .hour:
            let hour = min / 60
            min -= hour * 60
            return String(format: "%d:%02d:%02d", hour, min, second)

        case .minOrHour:
            let hour = min / 60
            if hour > 0 {
                min -= hour * 60
                return String(format: "%d:%02d:%02d", hour, min, second)
            } else {
                return String(format: "%02d:%02d", min, second)
            }

        case .millisecond:
            var time = self * 100
            let millisecond = time % 100
            time /= 100
            let sec = time % 60
            time /= 60
            let min = time % 60
            time /= 60
            let hour = time % 60
            if hour > 0 {
                return String(format: "%d:%02d:%02d.%02d", hour, min, sec, millisecond)
            } else {
                return String(format: "%02d:%02d.%02d", min, sec, millisecond)
            }
        }
    }
}

public extension FixedWidthInteger {
    var kmFormatted: String {
        Double(self).kmFormatted
    }
}

public enum Either<Left, Right> {
    case left(Left), right(Right)
}

public extension Either {
    init(_ left: Left, or _: Right.Type) { self = .left(left) }
    init(_ left: Left) { self = .left(left) }
    init(_ right: Right) { self = .right(right) }
    var left: Left? {
        if case let .left(value) = self {
            return value
        } else {
            return nil
        }
    }

    var right: Right? {
        if case let .right(value) = self {
            return value
        } else {
            return nil
        }
    }
}

public extension UIColor {
    convenience init?(assColor: String) {
        var colorString = assColor
        // 移除颜色字符串中的前缀 &H 和后缀 &
        if colorString.hasPrefix("&H") {
            colorString = String(colorString.dropFirst(2))
        }
        if colorString.hasSuffix("&") {
            colorString = String(colorString.dropLast())
        }
        if let hex = Scanner(string: colorString).scanInt(representation: .hexadecimal) {
            self.init(abgr: hex)
        } else {
            return nil
        }
    }

    convenience init(abgr hex: Int) {
        let alpha = 1 - (CGFloat(hex >> 24 & 0xFF) / 255)
        let blue = CGFloat((hex >> 16) & 0xFF)
        let green = CGFloat((hex >> 8) & 0xFF)
        let red = CGFloat(hex & 0xFF)
        self.init(red: red / 255.0, green: green / 255.0, blue: blue / 255.0, alpha: alpha)
    }

    convenience init(rgb hex: Int, alpha: CGFloat = 1) {
        let red = CGFloat((hex >> 16) & 0xFF)
        let green = CGFloat((hex >> 8) & 0xFF)
        let blue = CGFloat(hex & 0xFF)
        self.init(red: red / 255.0, green: green / 255.0, blue: blue / 255.0, alpha: alpha)
    }

    func createImage(size: CGSize = .one) -> UIImage {
        #if canImport(UIKit)
            let rect = CGRect(origin: .zero, size: size)
            UIGraphicsBeginImageContext(rect.size)
            let context = UIGraphicsGetCurrentContext()
            context?.setFillColor(cgColor)
            context?.fill(rect)
            let image = UIGraphicsGetImageFromCurrentImageContext()
            UIGraphicsEndImageContext()
            return image!
        #else
            let image = NSImage(size: size)
            image.lockFocus()
            drawSwatch(in: CGRect(origin: .zero, size: size))
            image.unlockFocus()
            return image
        #endif
    }
}

public protocol DisplayEnum: AnyObject {
    func set(frame: VideoVTBFrame, encoder: MTLRenderCommandEncoder)
}
