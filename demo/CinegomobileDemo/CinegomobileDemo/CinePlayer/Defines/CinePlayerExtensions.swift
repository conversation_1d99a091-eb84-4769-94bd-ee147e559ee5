//
//  Extensions.swift
//  VideoPlayer
//
//  Created by Zero on 2024/9/29.
//
import AVFoundation
import CFFmpeg
import CryptoKit
import Foundation
import UniformTypeIdentifiers

#if canImport(UIKit)
    import UIKit
#else
    import AppKit
#endif

#if !canImport(UIKit)

    public extension UIApplication {
        // MARK: - 播放时禁止自动熄屏

        private static var assertionID = IOPMAssertionID()
        static var isIdleTimerDisabled = false {
            didSet {
                if isIdleTimerDisabled != oldValue {
                    if isIdleTimerDisabled {
                        _ = IOPMAssertionCreateWithName(kIOPMAssertionTypeNoDisplaySleep as CFString,
                                                        IOPMAssertionLevel(kIOPMAssertionLevelOn),
                                                        "Is playing video" as CFString,
                                                        &assertionID)
                    } else {
                        _ = IOPMAssertionRelease(assertionID)
                    }
                }
            }
        }

        var isIdleTimerDisabled: Bool {
            get {
                UIApplication.isIdleTimerDisabled
            }
            set {
                UIApplication.isIdleTimerDisabled = newValue
            }
        }
    }

    public extension NSImage {
        convenience init(cgImage: CGImage) {
            self.init(cgImage: cgImage, size: NSSize.zero)
        }

        @available(macOS 11.0, *)
        convenience init?(systemName: String) {
            self.init(systemSymbolName: systemName, accessibilityDescription: nil)
        }
    }

#endif

extension CMTime {
    init(seconds: TimeInterval) {
        self.init(seconds: seconds, preferredTimescale: Int32(USEC_PER_SEC))
    }
}

extension CMTimeRange {
    init(start: TimeInterval, end: TimeInterval) {
        self.init(start: CMTime(seconds: start), end: CMTime(seconds: end))
    }
}

extension CGPoint {
    var reverse: CGPoint {
        CGPoint(x: y, y: x)
    }
}

public extension CGSize {
    static var one: CGSize {
        CGSize(width: 1, height: 1)
    }

    var reverse: CGSize {
        CGSize(width: height, height: width)
    }

    var toPoint: CGPoint {
        CGPoint(x: width, y: height)
    }

    var isHorizonal: Bool {
        width > height
    }

    /// 维持原有的比率。但是宽高不能超过size
    func within(size: CGSize?) -> CGSize {
        guard let size, height != 0, width != 0 else {
            return self
        }
        let aspectRatio = width / height
        return size.height * aspectRatio < size.width ? CGSize(width: Int(size.width), height: Int(size.width / aspectRatio)) : CGSize(width: Int(size.height * aspectRatio), height: Int(size.height))
    }

    func convert(rect: CGRect, toSize: CGSize) -> CGRect {
        guard height != 0, width != 0, toSize.width != 0, toSize.height != 0 else {
            return rect
        }
        let hZoom = toSize.width / width
        let vZoom = toSize.height / height
        let zoom = min(hZoom, vZoom)
        var newRect = rect * zoom
        let newDisplaySize = self * zoom
        newRect.origin.x += (toSize.width - newDisplaySize.width) / 2
        newRect.origin.y += (toSize.height - newDisplaySize.height) / 2
        return newRect.integral
    }
}

func * (left: CGSize, right: CGFloat) -> CGSize {
    CGSize(width: left.width * right, height: left.height * right)
}

func / (left: CGSize, right: CGFloat) -> CGSize {
    CGSize(width: left.width / right, height: left.height / right)
}

func * (left: CGPoint, right: CGFloat) -> CGPoint {
    CGPoint(x: left.x * right, y: left.y * right)
}

func / (left: CGPoint, right: CGFloat) -> CGPoint {
    CGPoint(x: left.x / right, y: left.y / right)
}

func * (left: CGRect, right: CGFloat) -> CGRect {
    CGRect(origin: left.origin * right, size: left.size * right)
}

func / (left: CGRect, right: CGFloat) -> CGRect {
    CGRect(origin: left.origin / right, size: left.size / right)
}

func - (left: CGSize, right: CGSize) -> CGSize {
    CGSize(width: left.width - right.width, height: left.height - right.height)
}

public extension URL {
    var isMovie: Bool {
        if let typeID = try? resourceValues(forKeys: [.typeIdentifierKey]).typeIdentifier {
            return UTType(typeID)?.conforms(to: .movie) ?? false
        }
        return false
    }

    var isAudio: Bool {
        if let typeID = try? resourceValues(forKeys: [.typeIdentifierKey]).typeIdentifier {
            return UTType(typeID)?.conforms(to: .audio) ?? false
        }
        return false
    }

    var isSubtitle: Bool {
        ["ass", "srt", "ssa", "vtt"].contains(pathExtension.lowercased())
    }

    var isPlaylist: Bool {
        ["cue", "m3u", "pls"].contains(pathExtension.lowercased())
    }

    func parsePlaylist() async throws -> [(String, URL, [String: String])] {
        let data = try await data()
        var entrys = data.parsePlaylist()
        for i in 0 ..< entrys.count {
            var entry = entrys[i]
            if entry.1.path.hasPrefix("./") {
                entry.1 = deletingLastPathComponent().appendingPathComponent(entry.1.path).standardized
                entrys[i] = entry
            }
        }
        return entrys
    }

    func data(userAgent: String? = nil) async throws -> Data {
        if isFileURL {
            return try Data(contentsOf: self)
        } else {
            var request = URLRequest(url: self)
            if let userAgent {
                request.addValue(userAgent, forHTTPHeaderField: "User-Agent")
            }
            let (data, _) = try await URLSession.shared.data(for: request)
            return data
        }
    }

    func string(userAgent: String? = nil, encoding: String.Encoding? = nil) async throws -> String? {
        let data = try await data(userAgent: userAgent)
        var string: String?
        let encodes = [encoding ?? String.Encoding.utf8,
                       String.Encoding(rawValue: CFStringConvertEncodingToNSStringEncoding(CFStringEncoding(CFStringEncodings.big5.rawValue))),
                       String.Encoding(rawValue: CFStringConvertEncodingToNSStringEncoding(CFStringEncoding(CFStringEncodings.GB_18030_2000.rawValue))),
                       String.Encoding.unicode]
        for encode in encodes {
            string = String(data: data, encoding: encode)
            if string != nil {
                break
            }
        }
        return string
    }

    func download(userAgent: String? = nil, completion: @escaping ((String, URL) -> Void)) {
        var request = URLRequest(url: self)
        if let userAgent {
            request.addValue(userAgent, forHTTPHeaderField: "User-Agent")
        }
        let task = URLSession.shared.downloadTask(with: request) { url, response, _ in
            guard let url, let response else {
                return
            }
            // 下载的临时文件要马上就用。不然可能会马上被清空
            completion(response.suggestedFilename ?? url.lastPathComponent, url)
        }
        task.resume()
    }
}

public extension Data {
    func parsePlaylist() -> [(String, URL, [String: String])] {
        guard let string = String(data: self, encoding: .utf8) else {
            return []
        }
        let scanner = Scanner(string: string)
        guard let symbol = scanner.scanUpToCharacters(from: .newlines) else {
            return []
        }
        if symbol.contains("#EXTM3U") {
            var entrys = [(String, URL, [String: String])]()
            while !scanner.isAtEnd {
                if let entry = scanner.parseM3U() {
                    entrys.append(entry)
                }
            }
            return entrys
        }
        if symbol.contains("[playlist]") {
            return scanner.parsePls()
        }
        return []
    }

    func md5() -> String {
        let digestData = Insecure.MD5.hash(data: self)
        return String(digestData.map { String(format: "%02hhx", $0) }.joined().prefix(32))
    }
}

extension Scanner {
    /// #EXTINF:-1 tvg-id="ExampleTV.ua" tvg-logo="https://image.com" group-title="test test", Example TV (720p) [Not 24/7]
    /// #EXTVLCOPT:http-referrer=http://example.com/
    /// #EXTVLCOPT:http-user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64)
    /// http://example.com/stream.m3u8
    func parseM3U() -> (String, URL, [String: String])? {
        if scanString("#EXTINF:") == nil {
            _ = scanUpToCharacters(from: .newlines)
            return nil
        }
        var extinf = [String: String]()
        if let duration = scanDouble() {
            extinf["duration"] = String(duration)
        }
        while scanString(",") == nil {
            let key = scanUpToString("=")
            _ = scanString("=\"")
            let value = scanUpToString("\"")
            _ = scanString("\"")
            if let key, let value {
                extinf[key] = value
            }
        }
        let title = scanUpToCharacters(from: .newlines)
        while scanString("#EXT") != nil {
            if scanString("VLCOPT:") != nil {
                let key = scanUpToString("=")
                _ = scanString("=")
                let value = scanUpToCharacters(from: .newlines)
                if let key, let value {
                    extinf[key] = value
                }
            } else {
                let key = scanUpToString(":")
                _ = scanString(":")
                let value = scanUpToCharacters(from: .newlines)
                if let key, let value {
                    extinf[key] = value
                }
            }
        }
        let urlString = scanUpToCharacters(from: .newlines)
        if let urlString, let url = URL(string: urlString) {
            return (title ?? url.lastPathComponent, url, extinf)
        }
        return nil
    }

    /// [playlist]
    ///
    /// File1=https://e20.yesstreaming.net:8279/
    /// Length1=-1
    ///
    /// File2=example2.mp3
    /// Title2=Just some local audio that is 2mins long
    /// Length2=120
    ///
    /// File3=F:\Music\whatever.m4a
    /// Title3=absolute path on Windows
    ///
    /// File4=%UserProfile%\Music\short.ogg
    /// Title4=example for an Environment variable
    /// Length4=5
    ///
    /// NumberOfEntries=4
    /// Version=2
    func parsePls() -> [(String, URL, [String: String])] {
//        var entrys = [(String, URL, [String: String])]()
        var urlMap = [Int: URL]()
        var titleMap = [Int: String]()
        var durationMap = [Int: String]()
        while !isAtEnd {
            if scanString("File") != nil {
                if let key = scanInt(), scanString("=") != nil, let value = scanUpToCharacters(from: .newlines), let url = URL(string: value) {
                    urlMap[key] = url
                }
            } else if scanString("Title") != nil {
                if let key = scanInt(), scanString("=") != nil, let value = scanUpToCharacters(from: .newlines) {
                    titleMap[key] = value
                }
            } else if scanString("Length") != nil {
                if let key = scanInt(), scanString("=") != nil, let value = scanUpToCharacters(from: .newlines) {
                    durationMap[key] = value
                }
            } else if scanString("NumberOfEntries") != nil || scanString("Version") != nil {
                break
            }
        }
        return urlMap.keys.sorted().compactMap { key in
            if let url = urlMap[key] {
                let title = titleMap[key]
                var extinf = [String: String]()
                extinf["duration"] = durationMap[key]
                return (title ?? url.lastPathComponent, url, extinf)
            } else {
                return nil
            }
        }
    }
}

extension URL: @retroactive Identifiable {
    public var id: Self { self }
}

extension String: @retroactive Identifiable {
    public var id: Self { self }
}

extension Float: @retroactive Identifiable {
    public var id: Self { self }
}

extension Array: @retroactive RawRepresentable where Element: Codable {
    public init?(rawValue: String) {
        guard let data = rawValue.data(using: .utf8),
              let result = try? JSONDecoder().decode([Element].self, from: data)
        else { return nil }
        self = result
    }

    public var rawValue: String {
        guard let data = try? JSONEncoder().encode(self),
              let result = String(data: data, encoding: .utf8)
        else {
            return "[]"
        }
        return result
    }
}

public extension NSError {
    convenience init(errorCode: CinePlayerErrorCode, avErrorCode: Int32) {
        let underlyingError = AVError(code: avErrorCode).message
        self.init(errorCode: errorCode, userInfo: [NSUnderlyingErrorKey: underlyingError])
    }
}

public extension Double {
    // MARK: - 格式化字节数到 G K M

    var kmFormatted: String {
        if self >= 1_000_000_000 {
            return String(format: "%.1fG", locale: Locale.current, self / 1_000_000_000)
        } else if self >= 1_000_000 {
            return String(format: "%.1fM", locale: Locale.current, self / 1_000_000)
        } else if self >= 10000, self <= 999_999 {
            return String(format: "%.1fK", locale: Locale.current, self / 1000)
        } else {
            return String(format: "%.0f", locale: Locale.current, self)
        }
    }
}

extension CGImage {
    static func combine(images: [(CGRect, CGImage)]) -> (CGRect, CGImage)? {
        if images.isEmpty {
            return nil
        }
        if images.count == 1 {
            return images[0]
        }

        let boundingRect = images.map(\.0).boundingRect()
        let bitsPerComponent = 8
        let bytesPerRow = 4 * Int(boundingRect.width)

        let image: CGImage? = autoreleasepool {
            let context = CGContext(
                data: nil,
                width: Int(boundingRect.width),
                height: Int(boundingRect.height),
                bitsPerComponent: bitsPerComponent,
                bytesPerRow: bytesPerRow,
                space: CGColorSpace(name: CGColorSpace.sRGB) ?? CGColorSpaceCreateDeviceRGB(),
                bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
            )

            if let context {
                for (rect, cgImage) in images {
                    context.draw(cgImage, in: rect.relativeRect(to: boundingRect))
                }
                return context.makeImage()
            }

            return nil
        }

        if let image {
            return (boundingRect, image)
        } else {
            return nil
        }
    }

    func data(type: AVFileType, quality: CGFloat) -> Data? {
        autoreleasepool {
            guard let mutableData = CFDataCreateMutable(nil, 0),
                  let destination = CGImageDestinationCreateWithData(mutableData, type.rawValue as CFString, 1, nil)
            else {
                return nil
            }
            CGImageDestinationAddImage(destination, self, [kCGImageDestinationLossyCompressionQuality: quality] as CFDictionary)
            guard CGImageDestinationFinalize(destination) else {
                return nil
            }
            return mutableData as Data
        }
    }

    func image(type: UTType = .png, quality: CGFloat = 0.2) -> UIImage? {
        // to AVFileType
        let type = AVFileType(type.identifier)
        if let data = data(type: type, quality: quality) {
            return UIImage(data: data)
        }
        return nil
    }

    static func make(rgbData: UnsafePointer<UInt8>, linesize: Int, width: Int, height: Int, isAlpha: Bool = false) -> CGImage? {
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapInfo: CGBitmapInfo = isAlpha ? CGBitmapInfo(rawValue: CGImageAlphaInfo.last.rawValue) : CGBitmapInfo.byteOrderMask
        guard let data = CFDataCreate(kCFAllocatorDefault, rgbData, linesize * height), let provider = CGDataProvider(data: data) else {
            return nil
        }

        return CGImage(width: width, height: height, bitsPerComponent: 8, bitsPerPixel: isAlpha ? 32 : 24, bytesPerRow: linesize, space: colorSpace, bitmapInfo: bitmapInfo, provider: provider, decode: nil, shouldInterpolate: false, intent: .defaultIntent)
    }
}

public extension AVFileType {
    static let png = UTType.png
}

extension [CGRect] {
    /// Find the bounding rect of all rect
    ///
    /// - Returns: A `CGRect` containing all  rectangles.
    func boundingRect() -> CGRect {
        guard let minX = map(\.minX).min(),
              let minY = map(\.minY).min(),
              let maxX = map(\.maxX).max(),
              let maxY = map(\.maxY).max() else { return .zero }
        return CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)
    }
}

extension CGRect {
    /// the x-coordinate of this rectangles center
    /// - note: Acts as a settable midX
    /// - returns: The x-coordinate of the center
    var centerX: CGFloat {
        get { midX }
        set { origin.x = newValue - width / 2 }
    }

    /// the y-coordinate of this rectangles center
    /// - note: Acts as a settable midY
    /// - returns: The y-coordinate of the center
    var centerY: CGFloat {
        get { midY }
        set { origin.y = newValue - height / 2 }
    }

    func relativeRect(to boundingRect: CGRect) -> CGRect {
        let origin = CGPoint(x: minX - boundingRect.minX, y: minY - boundingRect.minY)
        return CGRect(origin: origin, size: size)
    }
}

extension Array {
    init(tuple: (Element, Element, Element)) {
        self.init([tuple.0, tuple.1, tuple.2])
    }

    init(tuple: (Element, Element, Element, Element)) {
        self.init([tuple.0, tuple.1, tuple.2, tuple.3])
    }

    init(tuple: (Element, Element, Element, Element, Element, Element, Element, Element)) {
        self.init([tuple.0, tuple.1, tuple.2, tuple.3, tuple.4, tuple.5, tuple.6, tuple.7])
    }

    init(tuple: (Element, Element, Element, Element, Element, Element, Element, Element, Element)) {
        self.init([tuple.0, tuple.1, tuple.2, tuple.3, tuple.4, tuple.5, tuple.6, tuple.7, tuple.8])
    }

    var tuple8: (Element, Element, Element, Element, Element, Element, Element, Element) {
        (self[0], self[1], self[2], self[3], self[4], self[5], self[6], self[7])
    }

    var tuple4: (Element, Element, Element, Element) {
        (self[0], self[1], self[2], self[3])
    }

    /// 归并排序才是稳定排序。系统默认是快排
    func mergeSortBottomUp(isOrderedBefore: (Element, Element) -> Bool) -> [Element] {
        let n = count
        var z = [self, self] // the two working arrays
        var d = 0 // z[d] is used for reading, z[1 - d] for writing
        var width = 1
        while width < n {
            var i = 0
            while i < n {
                var j = i
                var l = i
                var r = i + width

                let lmax = Swift.min(l + width, n)
                let rmax = Swift.min(r + width, n)

                while l < lmax, r < rmax {
                    if isOrderedBefore(z[d][l], z[d][r]) {
                        z[1 - d][j] = z[d][l]
                        l += 1
                    } else {
                        z[1 - d][j] = z[d][r]
                        r += 1
                    }
                    j += 1
                }
                while l < lmax {
                    z[1 - d][j] = z[d][l]
                    j += 1
                    l += 1
                }
                while r < rmax {
                    z[1 - d][j] = z[d][r]
                    j += 1
                    r += 1
                }

                i += width * 2
            }

            width *= 2 // in each step, the subarray to merge becomes larger
            d = 1 - d // swap active array
        }
        return z[d]
    }
}
