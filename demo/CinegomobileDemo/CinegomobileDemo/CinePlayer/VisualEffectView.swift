
import SwiftUI

#if canImport(UIKit)
    public struct VisualEffectView: UIViewRepresentable {
        let blurStyle: UIBlurEffect.Style

        public init(_ blurStyle: UIBlurEffect.Style = .systemMaterial) {
            self.blurStyle = blurStyle
        }

        public func makeUIView(context _: Context) -> UIVisualEffectView {
            let blurEffect = UIBlurEffect(style: blurStyle)
            let visualEffectView = UIVisualEffectView(effect: blurEffect)
            return visualEffectView
        }

        public func updateUIView(_ uiView: UIVisualEffectView, context _: Context) {
            let blurEffect = UIBlurEffect(style: blurStyle)
            uiView.effect = blurEffect
        }
    }
#else

    public struct VisualEffectView: NSViewRepresentable {
        let material: NSVisualEffectView.Material
        let blendingMode: NSVisualEffectView.BlendingMode

        public init(
            _ material: NSVisualEffectView.Material = .sidebar,
            _ blendingMode: NSVisualEffectView.BlendingMode = .withinWindow
        ) {
            self.material = material
            self.blendingMode = blendingMode
        }

        public func makeNSView(context _: Context) -> NSVisualEffectView {
            let visualEffectView = NSVisualEffectView()
            visualEffectView.wantsLayer = true
            visualEffectView.layer?.backgroundColor = NSColor.clear.cgColor
            visualEffectView.blendingMode = blendingMode
            visualEffectView.state = .active
            visualEffectView.isEmphasized = true
            visualEffectView.material = material
            return visualEffectView
        }

        public func updateNSView(_ visualEffectView: NSVisualEffectView, context _: Context) {
            visualEffectView.wantsLayer = true
            visualEffectView.layer?.backgroundColor = NSColor.clear.cgColor
            visualEffectView.blendingMode = blendingMode
            visualEffectView.isEmphasized = true
            visualEffectView.material = material
        }
    }
#endif
