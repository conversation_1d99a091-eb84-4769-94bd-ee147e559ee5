
import Foundation

class EmbedSubtitleDataSouce: SubtitleInfo {
    var playItem: PlayerItem
    var subtitlesTrack: FFmpegStreamAsset
    var subtitleID: String
    var name: String
    var delay: TimeInterval

    init(playItem: PlayerItem, subtitlesTrack: FFmpegStreamAsset) {
        self.playItem = playItem
        self.subtitlesTrack = subtitlesTrack
        subtitleID = String(subtitlesTrack.streamIndex)
        name = subtitlesTrack.description
        delay = subtitlesTrack.delay
    }
}

extension EmbedSubtitleDataSouce: CineSubtitleProtocol {
    public func search(for time: TimeInterval, size: CGSize) async -> [SubtitlePart] {
        if let sutitleRender = subtitlesTrack.assImageRenderer {
            return await sutitleRender.search(for: time, size: size)
        }
        return subtitlesTrack.subtitleTrackDecode?.outputRenderQueue.search { item -> Bool in
            item.part.isEqual(time: time)
        }.map(\.part) ?? []
    }
}
