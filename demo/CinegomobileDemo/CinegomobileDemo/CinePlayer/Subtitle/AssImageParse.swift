
import Foundation
import libass
#if canImport(UIKit)
    import UIKit
#else
    import AppKit
#endif

public final class AssImageParse: SubtitleParseProtocol {
    public func canParse(scanner: Scanner) -> Bool {
        guard scanner.scanString("[Script Info]") != nil else {
            return false
        }
        return true
    }

    public func parsePart(scanner _: Scanner) -> SubtitlePart? {
        nil
    }

    public func parse(scanner: Scanner) -> CineSubtitleProtocol {
        AssImageRenderer(content: scanner.string)
    }
}

public final actor AssImageRenderer {
    private let library: OpaquePointer?
    private let renderer: OpaquePointer?
    private var currentTrack: UnsafeMutablePointer<ASS_Track>?
    private var addedSubtitles: Set<String> = []

    public init(content: String? = nil) {
        library = ass_library_init()
        renderer = ass_renderer_init(library)
        ass_set_extract_fonts(library, 1)
        ass_set_fonts_dir(library, PlayerOptions.fontsDir.path)
        ass_set_fonts(renderer, nil, nil, Int32(ASS_FONTPROVIDER_AUTODETECT.rawValue), nil, 1)
        if let content, var buffer = content.cString(using: .utf8) {
            currentTrack = ass_read_memory(library, &buffer, buffer.count, nil)
        } else {
            currentTrack = ass_new_track(library)
        }
    }

    public func processCodec(header: String) {
        if var buffer = header.cString(using: .utf8) {
            ass_process_codec_private(currentTrack, &buffer, Int32(buffer.count))
        }
    }

    // MARK: 添加字幕

    public func add(subtitle: String, start: Int64, duration: Int64) {
        // 检查是否已经添加过该字幕
        // 检查是否存在逗号
        if subtitle.contains(",") {
            guard let range = subtitle.range(of: ",") else { return }
            // 提取逗号之后的部分
            let subtitleContent = String(subtitle[range.upperBound...])

            // 检查是否已经添加过该字幕内容
            if addedSubtitles.contains(subtitleContent) {
                return
            }
            // 添加字幕内容到集合中
            addedSubtitles.insert(subtitleContent)
        }

        // TODO: 翻译处理

        // 添加
        if var buffer = subtitle.cString(using: .utf8) {
            ass_process_chunk(currentTrack, &buffer, Int32(buffer.count), start, duration)
        }
    }

    public func setFrame(size: CGSize) {
        let width = Int32(size.width * PlayerOptions.scale)
        let height = Int32(size.height * PlayerOptions.scale)
        ass_set_frame_size(renderer, width, height)
        ass_set_storage_size(renderer, width, height)
    }

    deinit {
        // 清理字幕轨道
        ass_free_track(currentTrack)
        ass_library_done(library)
        ass_renderer_done(renderer)
    }
}

extension AssImageRenderer: CineSubtitleProtocol {
    /// 生成图片
    public func image(for time: TimeInterval, changed: inout Int32) -> (CGRect, CGImage)? {
        let millisecond = Int64(time * 1000)

        guard let frame = ass_render_frame(renderer, currentTrack, millisecond, &changed) else {
            return nil
        }
        guard changed != 0 else {
            return nil
        }
        let images = frame.pointee.linkedImages()
        let boundingRect = images.map(\.imageRect).boundingRect()
        let imagePipeline: ImagePipelineType.Type

        if #available(iOS 16.0, tvOS 16.0, visionOS 1.0, macOS 13.0, macCatalyst 16.0, *), images.count <= 10 {
            imagePipeline = AccelerateImagePipeline.self
        } else {
            imagePipeline = BlendImagePipeline.self
        }
        guard let image = imagePipeline.process(images: images, boundingRect: boundingRect) else {
            return nil
        }

        return (boundingRect, image)
    }

    public func search(for time: TimeInterval, size: CGSize) async -> [SubtitlePart] {
        setFrame(size: size)
        var changed = Int32(0)
        guard let processedImage = image(for: time, changed: &changed) else {
            if changed == 0 {
                return []
            } else {
                return [SubtitlePart(time, .infinity, "")]
            }
        }
        let rect = processedImage.0 / PlayerOptions.scale
        let info = SubtitleImageInfo(rect: rect, image: UIImage(cgImage: processedImage.1), displaySize: size)
        let part = SubtitlePart(time, .infinity, image: info)
        return [part]
    }
}

/// Pipeline that processed an `ASS_Image` into a ``ProcessedImage`` that can be drawn on the screen.
public protocol ImagePipelineType {
    static func process(images: [ASS_Image], boundingRect: CGRect) -> CGImage?
}
