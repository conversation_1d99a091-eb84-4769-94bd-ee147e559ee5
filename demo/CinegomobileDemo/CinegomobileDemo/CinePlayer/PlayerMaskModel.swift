//
//  PlayerMaskModel.swift
//  VideoPlayer
//
//  Created by Zero on 2024/10/27.
//

import Foundation
#if os(macOS)
    import AppKit
#endif

// MARK: 播放器掩盖层控制

class PlayerMaskModel: ObservableObject {
    @Published private(set) var isMaskShow: Bool = true {
        didSet {
            if isMaskShow {
                #if os(macOS)
                    NSCursor.unhide()
                #endif
                delayHideTime = 3
                timer?.fireDate = Date.distantPast
            }
        }
    }

    private var delayHideTime: Double = 3
    private var timer: Timer?

    init() {
        startTimer()
    }

    deinit {
        stopTimer()
    }

    private func startTimer() {
        timer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            guard let self = self else { return }

            if self.delayHideTime <= 0 {
                self.isMaskShow = false
                #if os(macOS)
                    NSCursor.setHiddenUntilMouseMoves(true)
                #endif
            }
            if self.delayHideTime > 0 {
                self.delayHideTime -= 0.1
            }
        }
    }

    func stopTimer() {
        timer?.invalidate()
        timer = nil
        #if os(macOS)
            NSCursor.unhide()
        #endif
    }

    func showMask() {
        isMaskShow = true
    }

    func hideMask() {
        isMaskShow = false
    }
}
