# Set the GOPROXY environment variable
export GOPROXY=https://goproxy.io,direct
# Set environment variable allow bypassing the proxy for specified repos (optional)
# export GOPRIVATE=git.mycompany.com,github.com/my/private
export GOPRIVATE=git.cashub.cn

# FFmpeg 
# 在项目根目录创建 .envrc.local 文件，内容如下(根据自己的环境修改)：
# export CGO_LDFLAGS="-L/opt/homebrew/Cellar/ffmpeg/7.1_1/lib/"
# export CGO_CFLAGS="-I/opt/homebrew/Cellar/ffmpeg/7.1_1/include/"
# export PKG_CONFIG_PATH="/opt/homebrew/Cellar/ffmpeg/7.1_1/lib/pkgconfig"

# 引用 .envrc.local 文件
if [ -f .envrc.local ]; then
  source .envrc.local
fi