package cinegomobile

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"sync"
	"time"

	"git.cashub.cn/cinemore/cinemore-core/dao"
	"git.cashub.cn/cinemore/cinemore-core/pkg/cinelog"
	mediaserver "git.cashub.cn/cinemore/cinemore-core/rpc/media_server"
	rpcwebsocket "git.cashub.cn/cinemore/cinemore-core/rpc/rpc_websocket"
	"github.com/julienschmidt/httprouter"
)

// 60000不能用就累加，知道找到一个可用的端口，然后更新 HttpServerPort
var httpServerPort = 60000

func GetHTTPServerPort() int {
	return httpServerPort
}

// 查找可用端口
func findAvailablePort(startPort int) int {
	for port := startPort; port <= 65535; port++ {
		if isPortAvailable(port) {
			return port
		}
	}
	log.Fatal("No available ports found")
	return -1
}

// 检查端口是否可用
func isPortAvailable(port int) bool {
	listener, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
	if err != nil {
		return false
	}
	defer listener.Close() // 释放端口
	return true
}

// 定义一个结构体来管理 HTTP 服务器的生命周期
type httpServerManager struct {
	server *http.Server
	addr   string
	mu     sync.Mutex
}

// 启动 HTTP 服务器
func (m *httpServerManager) start() {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.server != nil {
		cinelog.Debug("HTTP server is already running")
		return
	}

	port := findAvailablePort(httpServerPort)
	httpServerPort = port

	m.addr = fmt.Sprintf(":%d", httpServerPort)
	m.server = &http.Server{
		Addr:    m.addr,
		Handler: newHTTPHandler(daoInstance),
	}

	go func() {
		cinelog.Debug("Server is running at", m.addr)
		if err := m.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatal("Server error:", err)
		}
		m.mu.Lock()
		m.server = nil // Server stopped, reset the pointer
		m.mu.Unlock()
		cinelog.Debug("HTTP server stopped")
	}()
}

// 停止 HTTP 服务器
func (m *httpServerManager) stop() {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.server == nil {
		cinelog.Debug("HTTP server is not running")
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := m.server.Shutdown(ctx); err != nil {
		cinelog.Error("HTTP server shutdown error:", err)
	}

	cinelog.Debug("Stopping HTTP server at", m.addr)
	m.server = nil
}

func newHTTPHandler(d *dao.Dao) http.Handler {
	// 使用 httprouter 作为主路由器
	router := httprouter.New()

	// 状态检查路由
	router.GET("/status", func(w http.ResponseWriter, r *http.Request, _ httprouter.Params) {
		w.Write([]byte("ok"))
	})

	// 媒体文件路由
	mediaserver.SetMediaFileRouter(router, d)

	// WebSocket 路由适配
	wsMux := http.NewServeMux()
	wsServer := rpcwebsocket.NewWebSocketServer(wsMux)
	wsServer.Start()

	// 将 WebSocket 路由挂载到 httprouter
	router.Handler("GET", "/ws/*path", http.StripPrefix("/ws", wsMux))

	return router
}
