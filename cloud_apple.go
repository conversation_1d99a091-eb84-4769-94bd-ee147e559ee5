//go:build apple

package cinegomobile

import (
	cloudapplesdk "cinegomobile/internal/cloud_apple_sdk"
	"cinegomobile/pb/appleCloudPB"
)

func CloudAppleRequest(IDFV string, route int32, data []byte) []byte {
	sdk := cloudapplesdk.NewClient(IDFV)
	switch route {
	case int32(appleCloudPB.Route_CreateUserDevice): // 创建用户设备
		return sdk.CreateUserDevice(data)
	case int32(appleCloudPB.Route_CreateAttributionBind): // 创建归因绑定
		return sdk.CreateAttributionBind(data)
	case int32(appleCloudPB.Route_CreateAppStoreOrder): // 创建 App Store 订单
		return sdk.CreateAppStoreOrder(data)
	case int32(appleCloudPB.Route_GetMarketingActivity): // 获取营销活动
		return sdk.GetMarketingActivity(data)
	}
	return nil
}
