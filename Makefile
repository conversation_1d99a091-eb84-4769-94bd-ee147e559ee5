
clean:
	go clean
	gomobile clean
	rm -rf ./release
	mkdir -p ./release

# target: macos,ios,appletvos,xros,iossimulator,appletvsimulator,xrsimulator
apple:
	make clean
	gomobile bind \
	-tags='apple' \
	-target='macos,ios,appletvos,xros' \
	-ldflags='-s -w' \
	-o ./release/Cinegomobile.xcframework

apple-dev:
	make clean
	gomobile bind \
	-tags='apple dev' \
	-target='macos,ios,appletvos,xros,iossimulator,appletvsimulator,xrsimulator' \
	-o ./release/Cinegomobile.xcframework

apple-ios:
	make clean
	gomobile bind \
	-tags='apple dev' \
	-target='macos,ios,iossimulator' \
	-o ./release/Cinegomobile.xcframework

apple-iossimulator:
	make clean
	gomobile bind \
	-tags='apple dev' \
	-target='iossimulator' \
	-o ./release/Cinegomobile.xcframework

apple-appletvsimulator:
	make clean
	gomobile bind \
	-tags='apple dev' \
	-target='appletvsimulator' \
	-o ./release/Cinegomobile.xcframework

flutter:
	make clean
	gomobile bind \
	-tags='android' \
	-target=android -androidapi=21 \
	-ldflags='-s -w' \
	-o ../cinemore-flutter/android/app/libs/cinegomobile.aar

flutter-dev:
	make clean
	gomobile bind \
	-tags='android dev' \
	-target=android -androidapi=21 \
	-o ../cinemore-flutter/android/app/libs/cinegomobile.aar

#cp -f ./release/cinegomobile.aar ./demo/CinegomobileFlutterDemo/cingomobile_flutter_demo/android/app/libs/


.PHONY: clean apple-dev apple flutter-dev flutter