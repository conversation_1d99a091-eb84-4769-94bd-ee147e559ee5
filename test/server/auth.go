package server

import (
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/serverPB"
)

func Login() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_Login,
		Body: &rpcPB.Request_Login{
			Login: &serverPB.LoginRequest{
				Username: "admin",
				Password: "admin",
			},
		},
	}
}

func LoginOrange() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_Login,
		Body: &rpcPB.Request_Login{
			Login: &serverPB.LoginRequest{
				Username: "admin",
				Password: "12345678",
			},
		},
	}
}

func LoginByCode() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_LoginByCode,
		Body: &rpcPB.Request_LoginByCode{
			LoginByCode: &serverPB.LoginByCodeRequest{
				Code: "515827",
			},
		},
	}
}

func Status() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_Status,
	}
}
