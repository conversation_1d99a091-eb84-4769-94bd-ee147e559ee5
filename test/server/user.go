package server

import (
	"git.cashub.cn/cinemore/cinemore-core/pb/modelPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/serverPB"
)

func UserList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_UserList,
		Body: &rpcPB.Request_UserList{
			UserList: &serverPB.UserListRequest{
				Page:     1,
				PageSize: 10,
				Keyword:  "",
			},
		},
	}
}

func UserGroupList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_UserGroupList,
	}
}

func UserCreate() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_UserCreate,
		Body: &rpcPB.Request_UserCreate{
			UserCreate: &modelPB.User{
				Username: "user",
				Nickname: "user",
				Password: "12345678",
				Group:    modelPB.UserGroup_UserGroupUser,
			},
		},
	}
}

func UserCreateAdmin() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_UserCreateAdmin,
		Body: &rpcPB.Request_UserCreateAdmin{
			UserCreateAdmin: &serverPB.UserCreateAdminRequest{
				Username: "admin",
				Nickname: "admin",
				Password: "12345678",
			},
		},
	}
}

func UserUpdate() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_UserUpdate,
		Body: &rpcPB.Request_UserUpdate{
			UserUpdate: &serverPB.UserUpdateRequest{
				Id:       2,
				Nickname: "user3",
				Password: "12345678",
			},
		},
	}
}

func UserDelete() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_UserDelete,
		Body: &rpcPB.Request_UserDelete{
			UserDelete: &serverPB.UserDeleteRequest{
				Id: 2,
			},
		},
	}
}
