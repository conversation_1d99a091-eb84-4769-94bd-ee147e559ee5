package server

import (
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/serverPB"
)

func ConnectLogin() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_ConnectLogin,
		Body: &rpcPB.Request_ConnectLogin{
			ConnectLogin: &serverPB.ConnectLoginRequest{
				Email:     "<EMAIL>",
				Password:  "12345678",
				CaptchaId: "bcYe9of4bFUbdMb6Frny",
				Captcha:   "130103",
			},
		},
	}
}

func ConnectCaptcha() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_ConnectCaptcha,
	}
}

func ConnectLogout() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_ConnectLogout,
	}
}

func ConnectUser() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_ConnectUser,
	}
}
