package server

import (
	"git.cashub.cn/cinemore/cinemore-core/pb/modelPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/serverPB"
)

func UserMediaPoolList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_UserMediaPoolList,
		Body: &rpcPB.Request_UserMediaPoolList{
			UserMediaPoolList: &serverPB.UserMediaPoolListRequest{
				Page:     1,
				PageSize: 10,
				UserId:   1,
				Type:     modelPB.UserMediaPoolType_UserMediaPoolTypeBlacklist,
				Keyword:  "",
			},
		},
	}
}

func UserMediaPoolCreate() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_UserMediaPoolCreate,
		Body: &rpcPB.Request_UserMediaPoolCreate{
			UserMediaPoolCreate: &serverPB.UserMediaPoolCreateRequest{
				UserId:     1,
				MetadataId: []int32{402, 403},
				Type:       modelPB.UserMediaPoolType_UserMediaPoolTypeBlacklist,
			},
		},
	}
}

func UserMediaPoolDelete() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_UserMediaPoolDelete,
		Body: &rpcPB.Request_UserMediaPoolDelete{
			UserMediaPoolDelete: &serverPB.UserMediaPoolDeleteRequest{
				Ids: []int32{3},
			},
		},
	}
}
