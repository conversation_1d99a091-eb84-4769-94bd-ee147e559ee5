package server

import (
	"git.cashub.cn/cinemore/cinemore-core/pb/modelPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/serverPB"
)

func TaskList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_TaskList,
		Body: &rpcPB.Request_TaskList{
			TaskList: &serverPB.TaskListRequest{
				Page:     1,
				PageSize: 10,
				Keyword:  "",
				Status:   modelPB.TaskStatus_TaskStatusProcessing,
			},
		},
	}
}

func TaskDetail() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_TaskDetail,
		Body: &rpcPB.Request_TaskDetail{
			TaskDetail: &serverPB.TaskDetailRequest{
				Id: 994,
			},
		},
	}
}

func TaskStatistic() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_TaskStatistic,
		Body: &rpcPB.Request_TaskStatistic{
			TaskStatistic: &serverPB.TaskStatisticRequest{
				Status:  0,
				Keyword: "",
			},
		},
	}
}

func TaskStage() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_TaskStage,
		Body: &rpcPB.Request_TaskStage{
			TaskStage: &serverPB.TaskStageRequest{
				TaskId: 1,
			},
		},
	}
}
