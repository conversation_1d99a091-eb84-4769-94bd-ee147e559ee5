package server

import (
	"fmt"
	"os"

	"git.cashub.cn/cinemore/cinemore-core/pb/modelPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/serverPB"
)

// 远程图片
func MetadataRemoteImage() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_MetadataRemoteImage,
		Body: &rpcPB.Request_MetadataRemoteImage{
			MetadataRemoteImage: &serverPB.MetadataRemoteImageRequest{
				Id:         "1",
				MediaType:  *modelPB.MediaType_MediaTypeMovie.Enum(),
				OriginType: modelPB.OriginType_OriginTypeTMDB,
				Language:   "en",
			},
		},
	}
}

// 选择远程图片
func MetadataSelectRemoteImage() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_MetadataSelectRemoteImage,
		Body: &rpcPB.Request_MetadataSelectRemoteImage{
			MetadataSelectRemoteImage: &serverPB.MetadataSelectRemoteImageRequest{
				Id:       1,
				ImageId:  0,
				Poster:   "",
				Backdrop: "",
				Logo:     "",
			},
		},
	}
}

// 上传本地图片
func MetadataUploadLocalImage() *rpcPB.Request {
	fileData, err := os.ReadFile("/Users/<USER>/Downloads/notion-avatar-1731485081692.png")
	if err != nil {
		fmt.Println(err)
		return nil
	}

	return &rpcPB.Request{
		Route: rpcPB.Route_MetadataUploadLocalImage,
		Body: &rpcPB.Request_MetadataUploadLocalImage{
			MetadataUploadLocalImage: &serverPB.MetadataUploadLocalImageRequest{
				Id:       1,
				FileName: "测试",
				FileData: fileData,
				Type:     "poster",
				ImageId:  23,
			},
		},
	}
}
