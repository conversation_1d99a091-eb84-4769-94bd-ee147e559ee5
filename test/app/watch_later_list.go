package app

import (
	"git.cashub.cn/cinemore/cinemore-core/pb/appPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/modelPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
)

func WatchLaterList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_WatchLaterList,
		Body: &rpcPB.Request_WatchLaterList{
			WatchLaterList: &appPB.WatchLaterListRequest{
				Page:     1,
				PageSize: 10,
				Type:     2, // 1 历史记录，2 稍后观看
			},
		},
	}
}

func WatchLaterCreate() *rpcPB.Request {
	seasonNumber := int32(1)
	episodeNumber := int32(1)
	return &rpcPB.Request{
		Route: rpcPB.Route_WatchLaterCreate,
		Body: &rpcPB.Request_WatchLaterCreate{
			WatchLaterCreate: &appPB.WatchLaterCreateRequest{
				Type: 2, // 1 历史记录，2 稍后观看
				File: &modelPB.File{
					Id: 2,
				},
				MetadataId:       1,
				PlaybackDuration: 210,
				SeasonNumber:     &seasonNumber,
				EpisodeNumber:    &episodeNumber,
			},
		},
	}
}

func WatchLaterDelete() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_WatchLaterDelete,
		Body: &rpcPB.Request_WatchLaterDelete{
			WatchLaterDelete: &appPB.WatchLaterDeleteRequest{
				Ids: []int32{1, 2, 3},
			},
		},
	}
}
