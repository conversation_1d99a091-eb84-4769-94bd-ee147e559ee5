package app

import (
	"git.cashub.cn/cinemore/cinemore-core/pb/appPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
)

func CreditCastList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_CreditCastList,
		Body: &rpcPB.Request_CreditCastList{
			CreditCastList: &appPB.CreditCastListRequest{
				MetadataId:      1,
				TvSeasonNumber:  1, // 季是必填的
				TvEpisodeNumber: 0, // 集暂时用不到
			},
		},
	}
}
