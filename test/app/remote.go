package app

import (
	"git.cashub.cn/cinemore/cinemore-core/pb/appPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/modelPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
)

func RemoteList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_RemoteList,
		Body: &rpcPB.Request_RemoteList{
			RemoteList: &appPB.RemoteListRequest{
				Page:     1,
				PageSize: 10,
			},
		},
	}
}

func RemoteCreate(baseURL string) *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_RemoteCreate,
		Body: &rpcPB.Request_RemoteCreate{
			RemoteCreate: &modelPB.Remote{
				Url:      baseURL,
				Remark:   "有多红",
				Username: "admin",
				Password: "12345678",
			},
		},
	}
}

func RemoteUpdate(baseURL string) *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_RemoteUpdate,
		Body: &rpcPB.Request_RemoteUpdate{
			RemoteUpdate: &modelPB.Remote{
				Id:       2,
				Url:      baseURL,
				Remark:   "有多红2",
				Username: "admin",
				Password: "12345678",
			},
		},
	}
}

func RemoteDelete() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_RemoteDelete,
		Body: &rpcPB.Request_RemoteDelete{
			RemoteDelete: &appPB.RemoteDeleteRequest{
				Ids: []int32{1, 2},
			},
		},
	}
}
