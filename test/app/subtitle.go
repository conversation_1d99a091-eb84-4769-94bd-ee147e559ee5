package app

import (
	"git.cashub.cn/cinemore/cinemore-core/pb/appPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/modelPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
	"git.cashub.cn/cinemore/cinemore-core/pkg/subtitle/assrt"
	"git.cashub.cn/cinemore/cinemore-core/pkg/subtitle/opensubtitles"
)

var (
	sourceOpensubtitles    = opensubtitles.Source
	sourceAssrt            = assrt.Source
	AssrtToken             = "HDAq6TDFnVrwl4tNQrg8oxzYmQ2tKAD4"
	AssrtToken2            = "MBrxonsNmZDOerqzRNZf2Tl3Cr2zMzWv"
	OpensubtitlesToken     = "E8aM3Xd3hSr0yJCBjQKTSMQKC0w8hBlr"
	OpensubtitlesUserToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJRRjVUdW5kdGU3NHlIZFE3dnJZNTJ1eThyNkY3TlNqdCIsImV4cCI6MTczMzU1NDU2MH0.k9hAXAr2VaKBBHNqzFw5dUDFeCM5mLWpWS_KLmhkj2s"
	baseURL                = "http://localhost:8080"
	token                  = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEsIkdyb3VwIjoxLCJleHAiOjE3NTE3MDEwNzksIm5iZiI6MTc0OTEwOTA3OSwiaWF0IjoxNzQ5MTA5MDc5fQ.rdC36T1jC0ees-e8ciu1ffEpIyX0SclyVY76qNsE_o4"
)

func SubtitleList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_SubtitleList,
		Body: &rpcPB.Request_SubtitleList{
			SubtitleList: &appPB.SubtitleListRequest{
				File: &modelPB.File{
					Id: 1,
				},
				BaseUrl: baseURL,
				Token:   token,
			},
		},
	}
}

func SubtitleDelete() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_SubtitleDelete,
		Body: &rpcPB.Request_SubtitleDelete{
			SubtitleDelete: &appPB.SubtitleDeleteRequest{
				Paths: []string{"data/subtitle/334/1/3/Clarksons.Farm.S02E01.ass"},
			},
		},
	}
}
