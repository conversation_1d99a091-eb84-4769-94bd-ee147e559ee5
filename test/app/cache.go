package app

import (
	"git.cashub.cn/cinemore/cinemore-core/pb/appPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/modelPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
)

func CacheList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_CacheList,
		Body: &rpcPB.Request_CacheList{
			CacheList: &appPB.CacheListRequest{
				Status: modelPB.CacheStatus_CacheStatusProcessing,
			},
		},
	}
}

func CacheCreate() *rpcPB.Request {
	baseURL := "http://localhost:8080"
	token := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEsIkdyb3VwIjoiYWRtaW4iLCJleHAiOjE3MzcxODI1NTQsIm5iZiI6MTczNDU5MDU1NCwiaWF0IjoxNzM0NTkwNTU0fQ.5QATSV37Fx7_ZfdjKCnUaDHC7N6lw7AEzRuQIcwb3xU"
	return &rpcPB.Request{
		Route: rpcPB.Route_CacheCreate,
		Body: &rpcPB.Request_CacheCreate{
			CacheCreate: &appPB.CacheCreateRequest{
				Cache: &modelPB.Cache{
					MediaType:       modelPB.MediaType_MediaTypeTV,
					Title:           "测试10",
					PosterOriginUrl: "http://123.com",
				},
				CacheItems: []*modelPB.CacheItem{
					{
						FileId:         2049,
						StillOriginUrl: "",
						BaseUrl:        baseURL,
						Token:          token,
					},
					{
						FileId:         2050,
						StillOriginUrl: "",
						BaseUrl:        baseURL,
						Token:          token,
					},
				},
			},
		},
	}
}

func CacheDelete() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_CacheDelete,
		Body: &rpcPB.Request_CacheDelete{
			CacheDelete: &appPB.CacheDeleteRequest{
				Ids: []int32{5},
			},
		},
	}
}

func CachePause() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_CachePause,
		Body: &rpcPB.Request_CachePause{
			CachePause: &appPB.CachePauseRequest{
				Ids:    []int32{12},
				Status: appPB.CachePauseRequestStatus_CachePauseStatusPause,
			},
		},
	}
}
