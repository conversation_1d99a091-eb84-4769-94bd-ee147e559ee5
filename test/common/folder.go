package common

import (
	"git.cashub.cn/cinemore/cinemore-core/pb/commonPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/modelPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
)

func FolderList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_FolderList,
		Body: &rpcPB.Request_FolderList{
			FolderList: &commonPB.FolderListRequest{
				StorageId: 1,
			},
		},
	}
}

func FolderCreate() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_FolderCreate,
		Body: &rpcPB.Request_FolderCreate{
			FolderCreate: &commonPB.CreateRequest{
				StorageId: 1,
				Files: []*modelPB.File{
					{
						Path: "/Media",
						// Path: "/Media/TV/人民的名义.S01",
						// Path: "/Media/TV/人民的名义.S01/人民的名义.S02E52.WEB-DL-MP4.2160P.4K.60fps高码高帧版.AIU.mp4",
					},
				},
			},
		},
	}
}

func FolderRefresh() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_FolderRefresh,
		Body: &rpcPB.Request_FolderRefresh{
			FolderRefresh: &commonPB.FolderRefreshRequest{
				StorageId: 1,
				FolderId:  40,
			},
		},
	}
}

func FolderDelete() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_FolderDelete,
		Body: &rpcPB.Request_FolderDelete{
			FolderDelete: &commonPB.FolderDeleteRequest{
				StorageId: 1,
				FolderIds: []int32{1},
			},
		},
	}
}
