package common

import (
	"git.cashub.cn/cinemore/cinemore-core/pb/commonPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/modelPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
)

func MetadataList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_MetadataList,
		Body: &rpcPB.Request_MetadataList{
			MetadataList: &commonPB.MetadataListRequest{
				Page:                1,
				PageSize:            99,
				Keyword:             "",
				ReleaseDateSort:     commonPB.SortType_SortTypeDefault, // 0 不排序 1 升序 2 降序
				VideoDurationSort:   commonPB.SortType_SortTypeDefault, // 0 不排序 1 升序 2 降序
				VoteAverageSort:     commonPB.SortType_SortTypeDefault, // 0 不排序 1 升序 2 降序
				CreateTimeSort:      commonPB.SortType_SortTypeDefault, // 0 不排序 1 升序 2 降序
				WatchType:           commonPB.WatchType_WatchTypeAll,   // 观看类型（可选）0：全部 1:已观看 2:未观看
				Year:                0,
				MetadataGenreId:     0,
				NetworkId:           0,
				Language:            "",
				Country:             "",
				VideoResolution:     "",
				MediaType:           modelPB.MediaType_MediaTypeAll,
				PersonId:            0,
				Offset:              0,
				ExcludeCollectionId: 0,
			},
		},
	}
}

func MetadataDetail() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_MetadataDetail,
		Body: &rpcPB.Request_MetadataDetail{
			MetadataDetail: &commonPB.MetadataDetailRequest{
				Id: 5,
			},
		},
	}
}

func MetadataRematchSearch() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_MetadataRematchSearch,
		Body: &rpcPB.Request_MetadataRematchSearch{
			MetadataRematchSearch: &commonPB.MetadataRematchSearchRequest{
				Title:     "猫和老鼠",
				Page:      1,
				Year:      0,
				MediaType: modelPB.MediaType_MediaTypeMovie,
			},
		},
	}
}

func MetadataRematchDataInName() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_MetadataRematchDataInName,
		Body: &rpcPB.Request_MetadataRematchDataInName{
			MetadataRematchDataInName: &commonPB.MetadataRematchDataInNameRequest{
				FilePath: "/Media/Share/[DBD-Raws][功夫熊猫3][1080P][BDRip][HEVC-10bit][中英外挂][FLAC][MKV]",
			},
		},
	}
}

func MetadataRematchFileList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_MetadataRematchFileList,
		Body: &rpcPB.Request_MetadataRematchFileList{
			MetadataRematchFileList: &commonPB.MetadataRematchFileListRequest{
				MetadataId: 234,
				FileId:     0,
			},
		},
	}
}

func MetadataRematchFolderList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_MetadataRematchFolderList,
		Body: &rpcPB.Request_MetadataRematchFolderList{
			MetadataRematchFolderList: &commonPB.MetadataRematchFolderListRequest{
				MetadataId: 0,
				FileId:     1173,
			},
		},
	}
}

func MetadataRematchTVWithFile() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_MetadataRematchTVWithFile,
		Body: &rpcPB.Request_MetadataRematchTVWithFile{
			MetadataRematchTVWithFile: &commonPB.MetadataRematchTVWithFileRequest{
				// MetadataId:      345,
				// Folder:          "/Media/Share/Game.of.Thrones.S01.2160p.UHD.BluRay.x265.10bit.HDR.TrueHD.7.1.Atmos-DON[rartv]",
				// OriginType:      "tmdb",
				// OriginId:        "71912",

				// StorageID: 1,
				// Folder:    "/Media/Download/qBittorrent/temp/The.Penthouse.S02.KOREAN.1080p.WEBRip.x265[eztv.re]",
				// // Folder:     "/Media/Share/What.If.S02.MULTI.2160p.WEB-DL.DOVI.HDR.H265-AOC/What.If.S02E01.MULTI.2160p.WEB-DL.DOVI.HDR.H265-AOC",
				// OriginType:      "tmdb",
				// OriginId:        "354912",

				// StorageID:  1,
				// Folder:     "/Media/Share/[DBD-Raws][功夫熊猫3][1080P][BDRip][HEVC-10bit][中英外挂][FLAC][MKV]",
				// OriginType: "tmdb",
				// OriginId:   "21494",

				// StorageID:  1,
				// Folder:     "/Media/TV/永夜星河.S01",
				// OriginType: "tmdb",
				// OriginId:   "127445",

				StorageId:  1,
				OriginType: modelPB.OriginType_OriginTypeTMDB,
				OriginId:   "341",
				FileId:     2,

				// StorageID:  1,
				// Folder:     "/Media/测试/东京爱情故事",
				// OriginType: "tmdb",
				// OriginId:   "341",
			},
		},
	}
}

func MetadataRematchMovie() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_MetadataRematchMovie,
		Body: &rpcPB.Request_MetadataRematchMovie{
			MetadataRematchMovie: &commonPB.MetadataRematchMovieRequest{
				OriginType: modelPB.OriginType_OriginTypeTMDB,
				OriginId:   "47480",
				FileId:     2227,
			},
		},
	}
}

func MetadataRematchTV() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_MetadataRematchTV,
		Body: &rpcPB.Request_MetadataRematchTV{
			MetadataRematchTV: &commonPB.MetadataRematchTVRequest{
				// OriginType: "tmdb",
				// OriginId:   "136891",
				// MediaType:  *commonPB.MediaType_TV.Enum(),
				// Title:      "突围",
				// MediaType:  *commonPB.MediaType_TV.Enum(),
				// Title:      "新猫和老鼠",
				OriginType: modelPB.OriginType_OriginTypeTMDB,
				OriginId:   "16123",
				// SeasonNumber: 1,
				// MediaType: *commonPB.MediaType_TV.Enum(),
				SetFile: []*commonPB.MetadataRematchTVRequest_SetFile{
					{
						FileId: 1191,
						// EpisodeNumber: -1,
						// EpisodeName:   "Episode 50",
					},
				},
			},
		},
	}
}
