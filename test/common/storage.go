package common

import (
	"git.cashub.cn/cinemore/cinemore-core/pb/commonPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/modelPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
)

func StorageCreate() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_StorageCreate,
		Body: &rpcPB.Request_StorageCreate{
			StorageCreate: &modelPB.Storage{
				Remark:   "test",
				Type:     modelPB.StorageType_StorageTypeSamba,
				Host:     "***********",
				User:     "ydh",
				Password: "ydh666666",
			},
			// StorageCreate: &commonPB.Storage{
			// 	Remark:     "test",
			// 	Type:     commonPB.StorageType_Samba,
			// 	Host:     "sv3.zhih.me",
			// 	Port:     55555,
			// 	User:     "ydh",
			// 	Password: "ydh666666",
			// },
		},
	}
}

func StorageList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_StorageList,
		Body: &rpcPB.Request_StorageList{
			StorageList: &commonPB.StorageListRequest{
				Page:     1,
				PageSize: 10,
				Keyword:  "",
			},
		},
	}
}

func StorageDetail() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_StorageDetail,
		Body: &rpcPB.Request_StorageDetail{
			StorageDetail: &commonPB.StorageDetailRequest{
				StorageId: 11,
			},
		},
	}
}

func StorageTypes() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_StorageTypes,
	}
}

func StorageField() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_StorageField,
		Body: &rpcPB.Request_StorageField{
			StorageField: &commonPB.StorageFieldRequest{
				Type: modelPB.StorageType_StorageTypeSamba,
			},
		},
	}
}

func StorageDriveAuthAddr() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_StorageCloudDriveAuthAddr,
		Body: &rpcPB.Request_StorageCloudDriveAuthAddr{
			StorageCloudDriveAuthAddr: &commonPB.StorageCloudDriveAuthAddrRequest{
				Type:        modelPB.StorageType_StorageTypeAlicloudDrive,
				Remark:      "",
				RedirectUri: "",
				State:       "",
			},
		},
	}
}

func StorageDriveAccessToken() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_StorageCloudDriveCreate,
		Body: &rpcPB.Request_StorageCloudDriveCreate{
			StorageCloudDriveCreate: &commonPB.StorageCloudDriveCreateRequest{
				Type:        modelPB.StorageType_StorageTypeAlicloudDrive,
				Code:        "",
				Remark:      "",
				RedirectUri: "",
				State:       "",
			},
		},
	}
}

func StorageRefresh() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_StorageRefresh,
		Body: &rpcPB.Request_StorageRefresh{
			StorageRefresh: &commonPB.StorageDefaultRequest{
				Id: 1,
			},
		},
	}
}

func StorageDelete() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_StorageDelete,
		Body: &rpcPB.Request_StorageDelete{
			StorageDelete: &commonPB.StorageDefaultRequest{
				Id: 1,
			},
		},
	}
}

func StorageUpdate() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_StorageUpdate,
		Body: &rpcPB.Request_StorageUpdate{
			StorageUpdate: &modelPB.Storage{
				Id:       1,
				Remark:   "测试 Samba2",
				Type:     modelPB.StorageType_StorageTypeSamba,
				Port:     445,
				Host:     "***********",
				User:     "ydh",
				Password: "ydh666666",
			},
		},
	}
}

func StorageDir() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_StorageDir,
		Body: &rpcPB.Request_StorageDir{
			StorageDir: &commonPB.StorageDirRequest{
				StorageId: 1,
				File: &modelPB.File{
					Path: "/",
				},
			},
		},
	}
}

// 获取云盘视频播放地址
func StorageCloudDriveVideoFilePlayUrl() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_StorageCloudDriveVideoFilePlayUrl,
		Body: &rpcPB.Request_StorageCloudDriveVideoFilePlayUrl{
			StorageCloudDriveVideoFilePlayUrl: &commonPB.StorageCloudDriveVideoFilePlayUrlRequest{
				StorageId: 6,
				File: &modelPB.File{
					// CloudFileId: "66d062c499ca5ac5be1a4910b94e1e14ba0695e2",// 阿里云盘
					CloudFileId: "3152164628251802671", // 115
				},
			},
		},
	}
}
