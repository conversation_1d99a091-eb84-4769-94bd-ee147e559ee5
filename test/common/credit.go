package common

import (
	"git.cashub.cn/cinemore/cinemore-core/pb/commonPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/modelPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
)

func CreditList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_CreditList,
		Body: &rpcPB.Request_CreditList{
			CreditList: &commonPB.CreditListRequest{
				Page:       1,
				PageSize:   50,
				CreditType: modelPB.CreditType_CreditTypeCrew,
				Job:        "Director", // 当选择 modelPB.CreditType_CreditTypeCrew 时，job字段必填
			},
		},
	}
}
