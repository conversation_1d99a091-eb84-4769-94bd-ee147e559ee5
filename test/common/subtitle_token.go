package common

import (
	"git.cashub.cn/cinemore/cinemore-core/model"
	"git.cashub.cn/cinemore/cinemore-core/pb/commonPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
)

func SubtitleTokenList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_SubtitleTokenList,
		Body: &rpcPB.Request_SubtitleTokenList{
			SubtitleTokenList: &commonPB.SubtitleTokenListRequest{
				Page:     1,
				PageSize: 10,
			},
		},
	}
}

func SubtitleTokenUpdate() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_SubtitleTokenUpdate,
		Body: &rpcPB.Request_SubtitleTokenUpdate{
			SubtitleTokenUpdate: &commonPB.SubtitleTokenUpdateRequest{
				Id:       18,
				ApiKey:   "E8aM3Xd3hSr0yJCBjQKTSMQKC0w8hBlr",
				Username: "juzipi",
				Password: "juzipi",
			},
		},
	}
}

func SubtitleTokenSourceField() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_SubtitleTokenSourceField,
		Body: &rpcPB.Request_SubtitleTokenSourceField{
			SubtitleTokenSourceField: &commonPB.SubtitleTokenSourceFieldRequest{
				Type: string(model.SourceOpensubtitles),
			},
		},
	}
}
