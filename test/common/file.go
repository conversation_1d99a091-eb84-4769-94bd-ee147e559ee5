package common

import (
	"git.cashub.cn/cinemore/cinemore-core/pb/commonPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/modelPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
)

func FileFileReadForPlayer() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_FileReadForPlayer,
		Body: &rpcPB.Request_FileReadForPlayer{
			FileReadForPlayer: &commonPB.FileReadForPlayer{
				StorageId:       1,
				MaxTransferSize: 0,
				File: &modelPB.File{
					Path: "/Media/A.Chinese.Odyssey.Part.Two.Cinderella.1995.CHINESE.1080p.BluRay.x265-VXT/A.Chinese.Odyssey.Part.Two.Cinderella.1995.CHINESE.1080p.BluRay.x265-VXT.mp4",
				},
			},
		},
	}
}
func FileDetail() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_FileDetail,
		Body: &rpcPB.Request_FileDetail{
			FileDetail: &commonPB.FileDetailRequest{
				File: &modelPB.File{
					Id: 12,
				},
			},
		},
	}
}
func FileList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_FileList,
		Body: &rpcPB.Request_FileList{
			FileList: &commonPB.FileListRequest{
				Page:     1,
				PageSize: 50,
				Keyword:  "",
			},
		},
	}
}
func FileDelete() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_FileDelete,
		Body: &rpcPB.Request_FileDelete{
			FileDelete: &commonPB.FileDeleteRequest{
				Ids: []int32{1002, 1003},
			},
		},
	}
}
