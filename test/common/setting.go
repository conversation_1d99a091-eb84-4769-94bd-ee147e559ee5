package common

import (
	"git.cashub.cn/cinemore/cinemore-core/pb/commonPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
)

func SettingLanguageList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_LanguageList,
		Body: &rpcPB.Request_LanguageList{
			LanguageList: &commonPB.SettingLanguageListRequest{
				Type: commonPB.LanguageType_Main,
			},
		},
	}
}

func SettingLanguageGetMain() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_LanguageGetMain,
	}
}

func SettingLanguageSetMain() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_LanguageSetMain,
		Body: &rpcPB.Request_LanguageSetMain{
			LanguageSetMain: &commonPB.SettingLanguageSetMainRequest{
				// Tag: "en-US",
				Tag: "zh-Hans",
			},
		},
	}
}
