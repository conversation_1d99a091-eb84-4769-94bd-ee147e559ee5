package common

import (
	"git.cashub.cn/cinemore/cinemore-core/pb/commonPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/modelPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
	"git.cashub.cn/cinemore/cinemore-core/pkg/subtitle/assrt"
	"git.cashub.cn/cinemore/cinemore-core/pkg/subtitle/opensubtitles"
)

var (
	sourceOpensubtitles   = opensubtitles.Source
	sourceAssrt           = assrt.Source
	AssrtApiKey           = "HDAq6TDFnVrwl4tNQrg8oxzYmQ2tKAD4"
	AssrtApiKey2          = "MBrxonsNmZDOerqzRNZf2Tl3Cr2zMzWv"
	OpensubtitlesApiKey   = "E8aM3Xd3hSr0yJCBjQKTSMQKC0w8hBlr"
	OpensubtitlesUsername = "juzipi"
	OpensubtitlesPassword = "juzipi"
	baseURL               = "http://localhost:8080"
	token                 = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEsIkdyb3VwIjoxLCJleHAiOjE3NTE3MDEwNzksIm5iZiI6MTc0OTEwOTA3OSwiaWF0IjoxNzQ5MTA5MDc5fQ.rdC36T1jC0ees-e8ciu1ffEpIyX0SclyVY76qNsE_o4"
)

func SubtitleImport() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_SubtitleImport,
		Body: &rpcPB.Request_SubtitleImport{
			SubtitleImport: &commonPB.SubtitleImportRequest{
				BaseURL: baseURL,
				Token:   token,
				File: &modelPB.File{
					Id: 5,
				},

				SubtitleFile: &modelPB.File{
					StorageId: 1,

					// Name:      "2_English.srt",
					// Path:      "/movies/Suits.S04.1080p.BluRay.x265-RARBG/Subs/Suits.S04E01.1080p.BluRay.x265-RARBG/2_English.srt",
					// Size:      64887,
					// FileType:  modelPB.FileType_FileTypeSubtitle,

					Name:     "Clarksons.Farm.S02E02.ass",
					Path:     "/Media/字幕/克拉克森的农场第二季 中英双语字幕/Clarksons.Farm.S02E02.ass",
					Size:     12345,
					FileType: modelPB.FileType_FileTypeSubtitle,

					// {
					// 	Id:   1403,
					// 	Path: "/Media/字幕/克拉克森的农场第二季 中英双语字幕/Clarksons.Farm.S02E03.ass",
					// },
					// {
					// 	Id:   1403,
					// 	Path: "/Media/字幕/克拉克森的农场第二季 中英双语字幕/Clarksons.Farm.S02E04.ass",
					// },
				},
			},
		},
	}
}

func SubtitleSearch() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_SubtitleSearch,
		Body: &rpcPB.Request_SubtitleSearch{
			SubtitleSearch: &commonPB.SubtitleSearchRequest{
				Page:    1,
				Keyword: "风雨同路",
				Source:  sourceAssrt,
				ApiKey:  AssrtApiKey,
			},
		},
	}
}

func SubtitleDownload() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_SubtitleDownload,
		Body: &rpcPB.Request_SubtitleDownload{
			SubtitleDownload: &commonPB.SubtitleDownloadRequest{
				BaseURL: baseURL,
				Token:   token,
				File: &modelPB.File{
					Id: 8,
				},
				Username: OpensubtitlesUsername,
				Password: OpensubtitlesPassword,
				Language: "zh",

				SubtitleId: 603934,
				Source:     sourceAssrt,
				ApiKey:     AssrtApiKey,

				// SubtitleId: 10537619,
				// Source:     sourceOpensubtitles,
				// ApiKey:     OpensubtitlesApiKey,

				// 	SubtitleId: 10537619,
				// 	Source:     sourceOpensubtitles,
				// 	ApiKey:     OpensubtitlesApiKey,
			},
		},
	}
}

func SubtitleLanguage() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_SubtitleLanguageList,
		Body: &rpcPB.Request_SubtitleLanguageList{
			SubtitleLanguageList: &commonPB.SubtitleLanguageListRequest{
				Source: sourceOpensubtitles,
				ApiKey: OpensubtitlesApiKey,

				// Source: sourceAssrt,
				// ApiKey: AssrtApiKey,

				Username: OpensubtitlesUsername,
				Password: OpensubtitlesPassword,
			},
		},
	}
}
