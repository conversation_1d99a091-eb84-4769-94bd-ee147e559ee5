package common

import (
	"git.cashub.cn/cinemore/cinemore-core/pb/commonPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
)

func CollectionList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_CollectionList,
	}
}

func CollectionItemList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_CollectionItemList,
		Body: &rpcPB.Request_CollectionItemList{
			CollectionItemList: &commonPB.CollectionItemListRequest{
				Page:         1,
				PageSize:     10,
				CollectionId: 12,
				Keyword:      "",
			},
		},
	}
}

func CollectionCreate() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_CollectionCreate,
		Body: &rpcPB.Request_CollectionCreate{
			CollectionCreate: &commonPB.CollectionCreateRequest{
				Name:        "Collection-02",
				Overview:    "Collection-02",
				MetadataIds: []int32{1, 2, 3},
			},
		},
	}
}

func CollectionItemCreate() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_CollectionItemCreate,
		Body: &rpcPB.Request_CollectionItemCreate{
			CollectionItemCreate: &commonPB.CollectionItemCreateRequest{
				CollectionId: 9,
				MetadataIds:  []int32{55, 56, 57},
			},
		},
	}
}

func CollectionUpdate() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_CollectionUpdate,
		Body: &rpcPB.Request_CollectionUpdate{
			CollectionUpdate: &commonPB.CollectionUpdateRequest{
				Id:       12,
				Name:     "Collection-011",
				Overview: "",
				SetTop:   false,
			},
		},
	}
}

func CollectionDelete() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_CollectionDelete,
		Body: &rpcPB.Request_CollectionDelete{
			CollectionDelete: &commonPB.CollectionDeleteRequest{
				Id: 262,
			},
		},
	}
}

func CollectionItemDelete() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_CollectionItemDelete,
		Body: &rpcPB.Request_CollectionItemDelete{
			CollectionItemDelete: &commonPB.CollectionItemDeleteRequest{
				CollectionId: 25,
				MetadataIds:  []int32{57},
			},
		},
	}
}

func CollectionRecommendList() *rpcPB.Request {
	return &rpcPB.Request{
		Route: rpcPB.Route_CollectionRecommendList,
	}
}
