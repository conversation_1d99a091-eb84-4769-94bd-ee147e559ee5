package main

import (
	"context"
	"encoding/json"
	"fmt"

	"cinegomobile/test/common"
	"cinegomobile/test/server"

	"git.cashub.cn/cinemore/cinemore-core/pb/modelPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
	rpcclient "git.cashub.cn/cinemore/cinemore-core/rpc/rpc_client"
	"github.com/jinzhu/copier"
)

const (
	sockAddr  = "/tmp/cinegomobile.sock"
	localhost = "http://localhost:8080"
	remote    = "http://***********:9000"
	remote2   = "http://***********:9999"
	remote3   = "http://**************:60102"
	remote4   = "http://**************:60103"
)

var (
	baseURL = localhost
	user    = &modelPB.Remote{}
)

func main() {
	// cinegomobile.StartServer(string(config.HostPlatformTypeLinux), "./", "/tmp/cinegomobile.sock")
	// Remote
	// SendRequest(app.RemoteCreate(baseURL))
	// SendRequest(app.RemoteList())
	// SendRequest(app.RemoteUpdate(baseURL))
	// SendRequest(app.RemoteDelete())

	// MARK: 远程登录
	// SendRequest(app.RemoteLogin(baseURL))
	// utils.PrintjsonMarshalIndent(user)
	// 模拟其他用户
	// user.Token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVc2VySWQiOjEsIkdyb3VwIjoiYWRtaW4iLCJleHAiOjE3MzI4MTIwOTcsIm5iZiI6MTczMDIyMDA5NywiaWF0IjoxNzMwMjIwMDk3fQ.iA3pPKDz-60JoFzd2aPH7r42OpSeptvYIWc2j2c3GCU"

	// MARK: Storage
	// SendRpcOverHttp(common.StorageCreate())
	// SendRpcOverHttp(common.StorageList())
	// SendRpcOverHttp(common.StorageDetail())
	// SendRequest(common.StorageCreate())
	// SendRequest(common.StorageList())

	// SendRpcOverHttp(common.StorageCloudDriveVideoFilePlayUrl())

	// SendRpcOverHttp(common.StorageTypes())
	// SendRequest(common.StorageTypes())

	// SendRpcOverHttp(common.StorageField())
	// SendRpcOverHttp(common.StorageDriveAuthAddr())
	// SendRpcOverHttp(common.StorageDriveAccessToken())

	// SendRpcOverHttp(common.StorageRefresh())
	// SendRpcOverHttp(common.StorageDelete())
	// SendRpcOverHttp(common.StorageUpdate())
	// SendRpcOverHttp(common.StorageDir())

	// MARK: Folder
	// SendRpcOverHttp(common.FolderList())
	// SendRpcOverHttp(common.FolderCreate())
	// SendRpcOverHttp(common.FolderDelete())
	// SendRequest(common.FolderCreate())
	// SendRpcOverHttp(common.FolderRefresh())

	// MARK: File
	// SendRpcOverHttp(common.FileDetail())
	// SendRpcOverHttp(common.FileList())
	// SendRpcOverHttp(common.FileDelete())

	// MARK: Setting
	// SendRpcOverHttp(common.SettingLanguageList())
	// SendRpcOverHttp(common.SettingLanguageGetMain())
	// SendRpcOverHttp(common.SettingLanguageSetMain())

	// MARK: Metadata app
	// SendRequest(app.MetadataSearchField())
	// SendRpcOverHttp(app.MetadataSearchField())
	// SendRpcOverHttp(app.MetadataBanner())

	// SendRpcOverHttp(common.MetadataList())
	// SendRpcOverHttp(common.MetadataDetail())
	// SendRpcOverHttp(app.MetadataStatistic())

	// MARK: 重新匹配
	// SendRpcOverHttp(common.MetadataRematchSearch()) // 搜索
	// SendRpcOverHttp(common.MetadataRematchDataInName()) // 获取文件路径中的数据
	// SendRpcOverHttp(common.MetadataRematchFileList())   // 文件
	// SendRpcOverHttp(common.MetadataRematchFolderList()) // 文件夹
	// SendRpcOverHttp(common.MetadataRematchTVWithFile()) // 选择剧季
	// SendRpcOverHttp(common.MetadataRematchTV())         // 匹配电视剧
	// SendRpcOverHttp(common.MetadataRematchMovie())      // 匹配电影

	// SendRpcOverHttp(server.MetadataRemoteImage())
	// SendRpcOverHttp(server.MetadataSelectRemoteImage())
	// SendRpcOverHttp(server.MetadataUploadLocalImage())

	// MARK: Collection
	// SendRpcOverHttp(common.CollectionList())
	// SendRpcOverHttp(common.CollectionItemList())
	// SendRpcOverHttp(common.CollectionCreate())
	// SendRpcOverHttp(common.CollectionItemCreate())
	// SendRpcOverHttp(common.CollectionUpdate())
	// SendRpcOverHttp(common.CollectionDelete())
	// SendRpcOverHttp(common.CollectionItemDelete())
	// SendRpcOverHttp(common.CollectionRecommendList()) // 推荐合集

	// MARK: Credit
	// SendRpcOverHttp(app.CreditCastList())
	// SendRpcOverHttp(common.CreditList())

	// MARK: Person
	// SendRpcOverHttp(app.PersonMetadata())

	// MARK: WatchLater
	// SendRequest(app.WatchLaterCreate())
	// SendRequest(app.WatchLaterList())
	// SendRequest(app.WatchLaterDelete())

	// MARK: Cache
	// SendRequest(app.CacheCreate())
	// SendRequest(app.CacheList())
	// SendRequest(app.CachePause())
	// SendRequest(app.CacheDelete())

	// MARK: Subtitle
	// SendRequest(app.SubtitleList())
	// SendRequest(app.SubtitleDelete())
	// SendRequest(common.SubtitleImport())
	// SendRequest(common.SubtitleSearch())
	// SendRequest(common.SubtitleDownload())
	// SendRpcOverHttp(common.SubtitleLanguage())

	// MARK: Subtitle Token
	// SendRequest(common.SubtitleTokenUpdate())
	// SendRequest(common.SubtitleTokenList())
	// SendRequest(common.SubtitleTokenSourceField())

	// SendRpcOverHttp(common.SubtitleTokenUpdate())
	// SendRpcOverHttp(common.SubtitleTokenList())
	// SendRpcOverHttp(common.SubtitleTokenSourceField())

	// MARK: Auth
	// SendRpcOverHttp(server.Login())
	// SendRpcOverHttp(server.Status())
	// loginResp, err := rpcclient.RemoteConnection(context.Background(), baseURL+"/api/user/rpc/auth", "", server.LoginByCode())
	// if err != nil {
	// 	fmt.Println("rpc err1:", err)
	// 	return
	// }
	// utils.PrintjsonMarshalIndent(loginResp)

	// MARK: ShortCode
	// shortCodeResp, err := rpcclient.RemoteConnection(context.Background(), baseURL+"/api/user/rpc/auth", "", server.ShortCodeGenre())
	// if err != nil {
	// 	fmt.Println("rpc err1:", err)
	// 	return
	// }
	// utils.PrintjsonMarshalIndent(shortCodeResp)

	// MARK: User
	// SendRpcOverHttp(server.UserCreate())
	// SendRpcOverHttp(server.UserCreateAdmin())
	// SendRpcOverHttp(server.UserList())
	// SendRpcOverHttp(server.UserGroupList())
	// SendRpcOverHttp(server.UserUpdate())
	// SendRpcOverHttp(server.UserDelete())

	// MARK: UserMediaPool
	// SendRpcOverHttp(server.UserMediaPoolCreate())
	// SendRpcOverHttp(server.UserMediaPoolList())
	// SendRpcOverHttp(server.UserMediaPoolDelete())

	// MARK: Task
	// SendRpcOverHttp(server.TaskList())
	// SendRpcOverHttp(server.TaskStatistic())
	// SendRpcOverHttp(server.TaskStage())
	// SendRpcOverHttp(server.TaskDetail())

	// MARK: Network
	// SendRpcOverHttp(common.NetworkList())

	// MARK: Genre
	// SendRpcOverHttp(common.GenreList())

	// SendLong()

	// MARK: Connect
	// SendRpcOverHttp(server.ConnectCaptcha())
	// SendRpcOverHttp(server.ConnectLogin())
	// SendRpcOverHttp(server.ConnectLogout())
	// SendRpcOverHttp(server.ConnectUser())

	// cinegomobile.GetRemotePromotionUser(2)

}

// SendRpcOverHttp 发送RPC请求
func SendRpcOverHttp(req *rpcPB.Request) {
	ctx := context.Background()
	resp, err := rpcclient.RemoteConnection(ctx, baseURL, user.Token, req)
	if err != nil {
		// 判断是否初始化
		statusResp, err := rpcclient.RemoteConnection(ctx, baseURL, "", server.Status())
		if err != nil {
			fmt.Println("status rpc err:", err)
			return
		}
		// 如果未初始化，则初始化
		if !statusResp.GetStatus().IsInstalled {
			createAdminResp, err := rpcclient.RemoteConnection(ctx, baseURL, "", server.UserCreateAdmin())
			if err != nil {
				fmt.Println("create admin rpc err:", err)
				return
			}
			user.Token = createAdminResp.GetCreateAdmin().Token

			// 设置语言为中文
			SendRpcOverHttp(common.SettingLanguageSetMain())

			// 创建文件源
			SendRpcOverHttp(common.StorageCreate())

			// 创建文件夹
			SendRpcOverHttp(common.FolderCreate())
		}

		if user.Token == "" {
			// 登录
			loginResp, err := rpcclient.RemoteConnection(ctx, baseURL, "", server.Login())
			if err != nil {
				loginResp, err = rpcclient.RemoteConnection(ctx, baseURL, "", server.LoginOrange())
				if err != nil {
					fmt.Println("rpc err1:", err)
					return
				}
			}
			user.Token = loginResp.GetLogin().Token

			resp, err = rpcclient.RemoteConnection(ctx, baseURL, user.Token, req)
			if err != nil {
				fmt.Println("rpc err2·:", err)
				return
			}
		}
	}
	if resp.Message != "success" {
		fmt.Println("msg err:", resp.Message)
		return
	}

	jsonBody, _ := json.MarshalIndent(resp.Response, "", "   ")
	fmt.Println(string(jsonBody))
}

// SendRequest 发送本地 rpc 请求
func SendRequest(req *rpcPB.Request) error {
	resp, err := Request(sockAddr, req)
	if err != nil {
		fmt.Println("remote err:", err)
		return err
	}
	if resp.Message != "success" {
		fmt.Println("remote msg err:", resp.Message)
		return err
	}

	if req.Route == rpcPB.Route_RemoteUpdate {
		remote := resp.GetRemoteUpdate().Remote
		copier.Copy(&user, remote)
		return nil
	}

	if resp.Message != "success" {
		fmt.Println("msg err:", resp.Message)
		return err
	}

	jsonBody, _ := json.MarshalIndent(resp.Response, "", "   ")
	fmt.Println(string(jsonBody))

	return nil
}

// SendLong 发送长连接
func SendLong() {
	err := rpcclient.RemoteLongConnection(context.Background())
	if err != nil {
		fmt.Println("rpc err:", err)
		return
	}
}
