package cinegomobile

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	mediainfo "cinegomobile/internal/media_info"

	"git.cashub.cn/cinemore/cinemore-core/config"
	"git.cashub.cn/cinemore/cinemore-core/core"
	"git.cashub.cn/cinemore/cinemore-core/core/bonjour"
	"git.cashub.cn/cinemore/cinemore-core/core/corepush"
	"git.cashub.cn/cinemore/cinemore-core/dao"
	"git.cashub.cn/cinemore/cinemore-core/pb/appPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/commonPB"
	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
	"git.cashub.cn/cinemore/cinemore-core/pkg/cinelog"
	"git.cashub.cn/cinemore/cinemore-core/pkg/localization"
	"git.cashub.cn/cinemore/cinemore-core/rpc/rpc_client"
	"git.cashub.cn/cinemore/cinemore-core/rpc/rpc_router"
	"git.cashub.cn/cinemore/cinemore-core/service"
	"google.golang.org/protobuf/proto"
)

// ServerState represents the current state of the server
type ServerState int32

const (
	ServerStopped ServerState = iota
	ServerRunning
	ServerStopping
)

var (
	daoInstance        *dao.Dao
	coreInstance       *core.Core
	router             *rpc_router.Router
	corePush           *corepush.CorePush
	bonjourInstance    *bonjour.Bonjour
	serverState        atomic.Int32
	serverRunningMutex sync.Mutex
	httpServerMgr      = &httpServerManager{}

	// rpcUnixSocketServer *rpc.UnixSocketServer

	// Configurable timeout values (in seconds)
	shutdownTimeout   = 2
	requestTimeout    = 10
	httpClientTimeout = 5

	// Reusable HTTP client
	httpClient = &http.Client{
		Timeout: time.Duration(httpClientTimeout) * time.Second,
	}

	// 系统信号通道
	quit = make(chan os.Signal, 1)
)

func init() {
	// Set maximum number of OS threads to run simultaneously
	runtime.GOMAXPROCS(runtime.NumCPU())

	// Initialize server state
	serverState.Store(int32(ServerStopped))

	// Setup signal monitoring in a goroutine
	go func() {
		signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
		for sig := range quit {
			cinelog.Debug(fmt.Sprintf("收到信号: %v, 开始停止服务", sig))
			stopServer()
		}
	}()
}

// 启动服务实例
// 参数 appPB.AppStartParams
func StartServer(data []byte) {
	appStartParams := &appPB.AppStartParams{}
	if err := proto.Unmarshal(data, appStartParams); err != nil {
		cinelog.Error("反序列化参数失败:", err)
		return
	}

	serverRunningMutex.Lock()
	defer serverRunningMutex.Unlock()

	if serverState.Load() == int32(ServerRunning) {
		cinelog.Debug("服务已经启动")
		return
	}

	cinelog.Debug("启动服务...")

	// 创建数据目录
	dataPath := filepath.Join(appStartParams.ContainerPath, "data")
	if err := os.MkdirAll(dataPath, 0o755); err != nil {
		cinelog.Error("无法创建数据目录:", err)
		return
	}

	// 设置配置，这一步一定要放在前面，因为后续的任何服务启动都可能用到
	config.HostPlatform = config.HostPlatformType(appStartParams.HostPlatform)
	config.DataPath = dataPath
	config.ContainerPath = appStartParams.ContainerPath
	config.IsRemoteMode = appStartParams.IsRemoteMode

	var err error

	// init dao
	daoInstance, err = dao.New(dataPath + "/cinemore.db")
	if err != nil {
		cinelog.Error("初始化DAO失败:", err)
		return
	}

	// 创建核心服务
	mediaInfoReader := &mediainfo.Mediainfo{}
	coreInstance = core.NewCore(daoInstance, mediaInfoReader)

	// 初始化服务实例
	serviceInstance := service.New(daoInstance, coreInstance)

	// 设置软件语言
	languageTag := localization.SelectLanguage(appStartParams.Language)
	localization.SetLanguage(languageTag)
	languageMainSchema := &commonPB.SettingLanguageSetMainRequest{
		Tag: languageTag,
	}
	serviceInstance.Common.Setting.LanguageMain(languageMainSchema)

	// 启动核心服务
	// 需要放在语言设置之后
	go coreInstance.Run()

	// 注册 rpc 路由
	router = rpc_router.NewRouter()
	router.SetAppRoute(daoInstance, serviceInstance)
	router.SetLocalRoute(daoInstance, coreInstance, serviceInstance)
	router.SetCommonRoute(daoInstance, coreInstance, serviceInstance)

	// 启动corepush服务
	corePush = corepush.NewCorePush(daoInstance, serviceInstance)
	corePush.Start()

	// 启动 HTTP 服务
	httpServerMgr.start()

	// 网络服务发现 - 仅非Android平台
	if config.HostPlatform != config.HostPlatformTypeAndroid {
		go func() {
			var err error
			bonjourInstance, err = bonjour.NewBonjour(coreInstance.Ctx)
			if err != nil {
				cinelog.Error("初始化Bonjour失败:", err)
				return
			}
			go bonjourInstance.DiscoverAll(10 * time.Second)
		}()
	}

	serverState.Store(int32(ServerRunning))

	cinelog.Debug(fmt.Sprintf("\n宿主平台: %s, \n数据目录: %s, \n语言: %s, \n远程模式: %t",
		appStartParams.HostPlatform, dataPath, languageTag, appStartParams.IsRemoteMode))

	go func() {
		<-config.StopChan
		quit <- syscall.SIGTERM
	}()
}

// 停止服务实例
// 这个不能让外部调用!!!
func stopServer() {
	// 使用CAS操作确保状态转换的原子性
	current := serverState.Load()
	if current != int32(ServerRunning) {
		// 如果不是Running状态，检查是否已经在停止中
		if current == int32(ServerStopping) {
			cinelog.Debug("服务正在停止中")
			return
		}

		cinelog.Debug("服务未运行，无需停止")
		return
	}

	// 设置为停止中状态
	serverState.Store(int32(ServerStopping))

	cinelog.Debug("正在关闭服务...")

	// 创建一个带超时时间的 context
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(shutdownTimeout)*time.Second)
	defer cancel()

	var wg sync.WaitGroup

	// 关闭 corepush 服务
	if corePush != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			corePush.Stop()
		}()
	}

	// // 关闭 rpc 服务
	// if rpcUnixSocketServer != nil {
	// 	wg.Add(1)
	// 	go func() {
	// 		defer wg.Done()
	// 		if err := rpcUnixSocketServer.Shutdown(); err != nil {
	// 			cinelog.Error("rpcServer关闭期间出错:", err)
	// 		}
	// 	}()
	// }

	// 关闭核心服务
	if coreInstance != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			coreInstance.Shutdown()
		}()
	}

	// 关闭 HTTP 服务
	wg.Add(1)
	go func() {
		defer wg.Done()
		httpServerMgr.stop()
	}()

	// 等待所有组件关闭或超时
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		cinelog.Debug("所有服务已正常关闭")
	case <-ctx.Done():
		cinelog.Error("服务关闭超时，强制退出")
	}

	// 在所有协程退出后，关闭数据库连接
	if daoInstance != nil {
		daoInstance.Close()
	}

	// 清理资源
	daoInstance = nil
	coreInstance = nil
	router = nil
	corePush = nil
	// rpcUnixSocketServer = nil

	serverState.Store(int32(ServerStopped))

	// 强制垃圾回收
	debug.FreeOSMemory()
	cinelog.Debug("服务已停止")
}

// App 中主动触发停止服务
func StopServer() {
	if serverState.Load() != int32(ServerRunning) {
		return
	}

	// 发送停止信号
	select {
	case config.StopChan <- true:
		// 给服务一些时间来清理资源
		time.Sleep(time.Duration(shutdownTimeout) * time.Second)
	default:
		cinelog.Error("无法发送停止信号，通道已满")
	}
}

// App 中重置应用
func ResetApp() {
	// 删除数据目录
	if err := os.RemoveAll(config.DataPath); err != nil {
		cinelog.Error("删除数据目录失败:", err)
	}

	// 先停止服务
	StopServer()
}

// App 中获取服务是否正在运行
func IsRunning() bool {
	return serverState.Load() == int32(ServerRunning)
}

// App 中获取服务是否正在停止
func IsStoping() bool {
	return serverState.Load() == int32(ServerStopping)
}

// App 中使用本地模式
func UseLocalMode() {
	config.IsRemoteMode = false
}

// App 中使用远程模式
func UseRemoteMode() {
	config.IsRemoteMode = true
}

// 宿主直接请求
// 优先使用这个方式
// 当这个方式不可用时，使用 RPC socket 请求
func Request(data []byte) []byte {
	var res *rpcPB.Response
	var err error

	if serverState.Load() == int32(ServerRunning) {
		// 反序列化请求
		req := &rpcPB.Request{}
		if err = proto.Unmarshal(data, req); err != nil {
			cinelog.Error("反序列化失败:", err)
			return encodeErrorResponse("反序列化失败:" + err.Error())
		}

		// 超时context
		ctx, cancel := context.WithTimeout(context.Background(), time.Duration(requestTimeout)*time.Second)
		defer cancel()

		// 处理请求，使用传入的 ctx
		res, err = router.Route(ctx, req)
		if err != nil {
			cinelog.Error("路由处理发生错误:", err)
			return encodeErrorResponse("路由处理发生错误:" + err.Error())
		}
	} else {
		cinelog.Error("服务未启动")
		res = &rpcPB.Response{
			Code:    1,
			Message: "服务未启动",
		}
	}

	// 序列化响应
	protoData, err := proto.Marshal(res)
	if err != nil {
		cinelog.Error("proto序列化失败:", err)
		return encodeErrorResponse("proto序列化失败:" + err.Error())
	}

	return protoData
}

// 辅助函数：编码错误响应
func encodeErrorResponse(errorMsg string) []byte {
	res := &rpcPB.Response{
		Code:    1,
		Message: errorMsg,
	}

	data, err := proto.Marshal(res)
	if err != nil {
		// 如果连序列化错误响应都失败了，返回一个简单的错误字符串
		return []byte(errorMsg)
	}

	return data
}

func GetRemotePromotionUser(id int32) []byte {
	r := daoInstance.GenDB.Remote

	remote, err := r.Where(r.Id.Eq(id)).First()
	if err != nil {
		return encodeErrorResponse(err.Error())
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	resp, err := rpc_client.RemoteConnection(ctx, remote.Url, remote.Token, &rpcPB.Request{
		Route: rpcPB.Route_ConnectPromotionUser,
	})
	if err != nil {
		return encodeErrorResponse(err.Error())
	}
	promotionUser := resp.GetConnectUser()

	// 序列化响应
	protoData, err := proto.Marshal(promotionUser)
	if err != nil {
		cinelog.Error("proto序列化失败:", err)
		return encodeErrorResponse("proto序列化失败:" + err.Error())
	}

	return protoData
}
