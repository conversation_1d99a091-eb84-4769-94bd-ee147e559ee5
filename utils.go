package cinegomobile

import (
	"context"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"git.cashub.cn/cinemore/cinemore-core/config"
	"git.cashub.cn/cinemore/cinemore-core/pkg/cinelog"
	"git.cashub.cn/cinemore/cinemore-core/pkg/download"
	"git.cashub.cn/cinemore/cinemore-core/pkg/utils"
)

// 请求网络权限
func NetworkInit() {
	// 创建一个新的请求
	req, err := http.NewRequest("GET", "http://www.baidu.com", nil)
	if err != nil {
		cinelog.Error("创建网络请求失败:", err)
		return
	}

	// 使用全局HTTP客户端发送请求
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	req = req.WithContext(ctx)
	resp, err := httpClient.Do(req)
	if err != nil {
		cinelog.Error("发送网络请求失败:", err)
		return
	}
	defer resp.Body.Close()
}

// 网络发现
func NetworkDiscover() {
	if bonjourInstance != nil {
		go bonjourInstance.DiscoverAll(10 * time.Second)
	}
}

// 通过传入的图片地址，缓存图片到本地，返回缓存后的图片地址
// maxWidth 为图片的最大宽度, 传入 0 表示不限制
func GetImageCache(imageURL string, maxWidth int) string {
	if config.DataPath == "" {
		return imageURL
	}

	if imageURL == "" {
		return ""
	}

	// 图片缓存目录
	imagesFolder := filepath.Join(config.DataPath, "imageCaches")

	// 后缀 tmdb 的 .svg 不支持缩放
	postfix := filepath.Ext(imageURL)

	// 指定尺寸版本
	reSizeImageURL := imageURL
	if maxWidth != 0 && postfix != ".svg" && postfix != ".gif" {
		// tmdb 支持指定图片宽度 只支持 500
		// 判断是否是 tmdb 的图片
		if strings.Contains(reSizeImageURL, "image.tmdb.org") {
			if maxWidth != 500 {
				maxWidth = 500
			}
			// replace https://image.tmdb.org/t/p/original to https://image.tmdb.org/t/p/w{maxWidth}
			reSizeImageURL = strings.Replace(reSizeImageURL, "original", "w"+strconv.Itoa(maxWidth), 1)
		}
		// 本地缓存目录 指定尺寸版本
		imagesFolder = filepath.Join(imagesFolder, strconv.Itoa(maxWidth))
	}

	// 创建缓存目录（如果不存在）
	if err := os.MkdirAll(imagesFolder, 0o755); err != nil {
		cinelog.Error("创建图片缓存目录失败:", err)
		return imageURL
	}

	// 从网络下载图片
	filename := filepath.Base(reSizeImageURL)
	filePath, err := download.DownloadImageIfNotExist(reSizeImageURL, imagesFolder, filename)
	if err != nil {
		cinelog.Error("下载图片失败:", err)
		return imageURL
	}

	return filePath
}

// 获取父目录
func GetParentDir(dirctory string) string {
	return filepath.Dir(dirctory)
}

// 格式化视频时长为秒 "02:51:13"
// 获取已观看百分比计算：已观看的秒数 / 总秒数 * 100
//
// 例：
// num := 3000 // 已观看的秒数
// fmt.Printf("已观看时间: %.2f %%\n", float64(num)/float64(sum)*100)
func FormatVideoDurationSecond(timeStr string) int {
	return utils.ParseDuration(timeStr)
}

// FormatSecoudStr 格式化视频时长为秒 "02:51:13"
func FormatSecoudStr(secoud int64) string {
	return utils.FormatDuration(secoud)
}
