## 初始化

## gomobile

```shell
# 安装gomobile
go install golang.org/x/mobile/cmd/gomobile

# 初始化
gomobile init

# 每次 go mod tidy 后需要执行
go get golang.org/x/mobile/cmd/gomobile
```

## 自动设置环境变量

```shell
# 安装 direnv
brew install direnv
```
### 配置

#### 方法一

在 ~/.zshrc 中找到 plugins=(...) 这一行，添加 direnv， 如 plugins=(git direnv)

#### 方法二

将以下内容添加到 ~/.zshrc 文件中

``` shell
eval "$(direnv hook zsh)"
```

### 使用

生效项目专属环境变量

```shell
direnv allow
```

## go work 工作空间

项目中使用了私有仓库 `git.cashub.cn/cinemore/cinemore-core` 中的包，需要同时开发 `cinemore-core` 时，就用到工作空间

这样可以重定向远程的包到本地，方便调试

注意：go.work go.work.sum 文件不要提交到远程仓库

```shell
# 创建工作空间
go work init
```

## 设置工作空间

比如 `cinemore-core` 项目在上一级目录下时

go.work 文件内容如下

```shell
go 1.22.5

use .
use ../cinemore-core

# 也可以用以下方式
# replace git.cashub.cn/cinemore/cinemore-core => ../cinemore-core
```

## git config

设置私有仓库的拉取方式为 ssh

```shell
git config --global url."*****************:".insteadOf "https://git.cashub.cn/"
```

# 编译需要安装 ffmpeg

```shell  
brew install ffmpeg libass
```

# gomobile编译 tvos, xros 需要合并 PR

进入到一个空目录，执行以下命令

```shell
<NAME_EMAIL>:golang/mobile.git

cd mobile

git fetch origin pull/102/head:pr-102

git merge pr-102

go install ./cmd/gobind
go install ./cmd/gomobile
```

# 其他

apple 平台 GODEBUG=asyncpreemptoff=1 变量要放在xcode项目变量配置中

Product -> Schemes -> Edit Schemes -> Run -> Arguments -> Environment Variables