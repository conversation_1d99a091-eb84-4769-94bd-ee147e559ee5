package cloudapplesdk

import (
	"cinegomobile/pb/appleCloudPB"

	"google.golang.org/protobuf/proto"
)

// CreateUserDevice 创建用户设备
func (c *Client) CreateUserDevice(data []byte) []byte {
	resp := appleCloudPB.Response{
		Code:    0,
		Message: "success",
	}

	_, err := c.makeRequest("POST", "/user-devices", data)
	if err != nil {
		resp.Code = 1
		resp.Message = err.Error()
	}

	protoData, err := proto.Marshal(&resp)
	if err != nil {
		return nil
	}

	return protoData
}

// CreateAttributionBind 创建归因绑定
func (c *Client) CreateAttributionBind(data []byte) []byte {
	resp := appleCloudPB.Response{
		Code:    0,
		Message: "success",
	}

	_, err := c.makeRequest("POST", "/attribution-binds", data)
	if err != nil {
		resp.Code = 1
		resp.Message = err.Error()
	}

	protoData, err := proto.Marshal(&resp)
	if err != nil {
		return nil
	}

	return protoData
}

// CreateAppStoreOrder 创建 App Store 订单
func (c *Client) CreateAppStoreOrder(data []byte) []byte {
	resp := appleCloudPB.Response{
		Code:    0,
		Message: "success",
	}

	_, err := c.makeRequest("POST", "/app-store-orders", data)
	if err != nil {
		resp.Code = 1
		resp.Message = err.Error()
	}
	protoData, err := proto.Marshal(&resp)
	if err != nil {
		return nil
	}

	return protoData
}

// GetMarketingActivity 获取营销活动详情
func (c *Client) GetMarketingActivity(data []byte) []byte {
	resp, err := c.makeRequest("POST", "/marketing-activities", data)
	if err != nil {
		resp := appleCloudPB.Response{
			Code:    1,
			Message: err.Error(),
		}
		protoData, err := proto.Marshal(&resp)
		if err != nil {
			return nil
		}
		return protoData
	} else {
		return resp
	}
}
