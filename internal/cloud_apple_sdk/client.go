package cloudapplesdk

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"
)

const (
	baseURLProduction = "https://cinemore.com.cn/cloud/api/apple"
	baseURLLocalhost  = "http://localhost:18000/api/apple"

	BaseURL = baseURLLocalhost
)

// Client Apple API 客户端
type Client struct {
	IdentifierForVendor string
}

var HTTPClient = &http.Client{
	Timeout: 30 * time.Second,
}

// NewClient 创建新的 Apple API 客户端
func NewClient(identifierForVendor string) *Client {
	return &Client{
		IdentifierForVendor: identifierForVendor,
	}
}

// generateSign 生成签名
func (c *Client) generateSign(timestamp string) string {
	signStr := "IdentifierForVendor" + c.IdentifierForVendor + "Timestamp" + timestamp
	h := md5.New()
	io.Copy(h, strings.NewReader(signStr))
	return hex.EncodeToString(h.Sum(nil))
}

// makeRequest 发送HTTP请求
func (c *Client) makeRequest(method, path string, body []byte) ([]byte, error) {
	url := strings.TrimSuffix(BaseURL, "/") + "/" + strings.TrimPrefix(path, "/")

	var reqBody io.Reader
	if body != nil {
		reqBody = bytes.NewReader(body)
	} else {
		reqBody = nil
	}

	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/protobuf")

	// 添加签名认证
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	sign := c.generateSign(timestamp)

	req.Header.Set("Sign", sign)
	req.Header.Set("IdentifierForVendor", c.IdentifierForVendor)
	req.Header.Set("Timestamp", timestamp)

	resp, err := HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("send request: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("read response: %w", err)
	}

	return respBody, nil
}
