package rpcclient

import (
	"fmt"
	"net"

	"git.cashub.cn/cinemore/cinemore-core/pb/rpcPB"
	"git.cashub.cn/cinemore/cinemore-core/rpc/protocol"
	"google.golang.org/protobuf/proto"
)

func SendRPCRequest(conn net.Conn, req *rpcPB.Request) (*rpcPB.Response, error) {
	// 序列化请求数据
	reqData, err := proto.Marshal(req)
	if err != nil {
		err = fmt.Errorf("序列化请求数据发生错误: %s", err)
		return nil, err
	}

	// 发送请求数据
	err = protocol.SendRPCData(conn, 1, 0, reqData)
	if err != nil {
		err = fmt.Errorf("发送请求数据发生错误: %s", err)
		return nil, err
	}

	// 读取响应数据
	_, resData, err := protocol.ReadRPCData(conn)
	if err != nil {
		err = fmt.Errorf("读取响应数据发生错误: %s", err)
		return nil, err
	}

	// 反序列化响应
	res := &rpcPB.Response{}
	if err := proto.Unmarshal(resData, res); err != nil {
		err = fmt.Errorf("反序列化响应数据发生错误: %s", err)
		return nil, err
	}

	return res, nil
}
