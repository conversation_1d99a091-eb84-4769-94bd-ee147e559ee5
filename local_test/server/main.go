package main

import (
	"os"
	"os/signal"
	"syscall"

	"cinegomobile"

	"git.cashub.cn/cinemore/cinemore-core/config"
	"git.cashub.cn/cinemore/cinemore-core/pb/appPB"
	"google.golang.org/protobuf/proto"
)

// 系统信号通道
var quit = make(chan os.Signal, 1)

func main() {
	appStartParams := &appPB.AppStartParams{
		HostPlatform:  string(config.HostPlatformTypeiOS),
		ContainerPath: "./",
		Language:      "zh-Hans",
		IsRemoteMode:  false,
	}

	// 将 Protobuf 消息序列化为二进制数据
	serializedData, err := proto.Marshal(appStartParams)
	if err != nil {
		panic("序列化参数失败: " + err.Error())
	}

	cinegomobile.StartServer(serializedData)

	go func() {
		<-config.<PERSON><PERSON><PERSON>
		quit <- syscall.SIGTERM
	}()

	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	<-quit

	cinegomobile.StopServer()
}
